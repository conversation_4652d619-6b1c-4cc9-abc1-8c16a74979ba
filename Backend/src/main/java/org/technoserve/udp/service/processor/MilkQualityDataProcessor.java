package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.dataflow.MilkQuality;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.MilkQualityRepository;
import org.technoserve.udp.service.processor.validator.MilkQualityValidator;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Processor for Milk Quality data
 */
public class MilkQualityDataProcessor extends AbstractDataProcessor<MilkQuality> {

  private final MilkQualityRepository milkQualityRepository;
  private final CentreRepository centreRepository;

  public MilkQualityDataProcessor(MilkQualityRepository milkQualityRepository, CentreRepository centreRepository, FormulaEvaluator evaluator) {
    super(new MilkQualityValidator(centreRepository, milkQualityRepository), evaluator);
    this.milkQualityRepository = milkQualityRepository;
    this.centreRepository = centreRepository;
  }

  @Override
  protected MilkQuality createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    return MilkQuality.builder()
        .programId(program.getProgramId())
        .partnerId(partner.getPartnerId())
        .excelFileMetaData(excelFileMetaData)
        .build();
  }

  @Override
  protected void processIdField(Row row, Map<Integer, String> columnIndices, MilkQuality entity, Map<String, String> validationErrors, Map<String, String> mappings) {
    // Process Centre ID
    String centreIdExcelColumn = mappings.get("centreId");
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    String centreId = columnValues.get(centreIdExcelColumn);
    if (centreId == null || centreId.isEmpty()) {
      validationErrors.put("Centre ID", "Centre ID is required");
    } else {
      entity.setCentreId(centreId);
    }

    // Process Date
    String dateExcelColumn = mappings.get("date");
    String dateStr = columnValues.get(dateExcelColumn);
    if (dateStr == null || dateStr.isEmpty()) {
      validationErrors.put("Date", "Date is required");
    } else {
      try {
        // Try different date formats
        LocalDate date = parseDate(dateStr);
        entity.setDate(date);
      } catch (DateTimeParseException e) {
        validationErrors.put("Date", "Invalid date format: " + dateStr);
      }
    }
  }


  @Override
  protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices, Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    List<MilkQuality> milkQualityToSave = new ArrayList<>();
    int batchRecordsProcessed = 0;

    // Process each row in the batch
    for (int i = startRow; i <= endRow; i++) {
      Row row = sheet.getRow(i);
      if (row == null) continue;

      // Process the row and get the milk quality entity
      MilkQuality milkQuality = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);

      // Add to batch if row has data
      if (milkQuality != null) {
        milkQualityToSave.add(milkQuality);
        batchRecordsProcessed++;
      }
    }

    // Save all entities in the batch
    if (!milkQualityToSave.isEmpty()) {
      milkQualityRepository.saveAll(milkQualityToSave);
    }

    return batchRecordsProcessed;
  }

  /**
   * Process a single row and return the milk quality entity
   *
   * @param row           The Excel row
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return The milk quality entity, or null if the row has no data
   */
  private MilkQuality processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings,
                                 Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    // Create a new MilkQuality object or find an existing one by centreId and date if available
    MilkQuality milkQuality = null;
    String centreId = null;
    LocalDate date = null;

    // First, collect all values from the row into a HashMap
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    // Check if the row has any data
    if (columnValues.isEmpty()) {
      return null;
    }

    // Get centreId and date from the row
    String centreIdExcelColumn = mappings.get("centreId");
    String dateExcelColumn = mappings.get("date");

    if (centreIdExcelColumn != null && dateExcelColumn != null) {
      centreId = columnValues.get(centreIdExcelColumn);
      String dateStr = columnValues.get(dateExcelColumn);

      if (centreId != null && !centreId.isEmpty() && dateStr != null && !dateStr.isEmpty()) {
        try {
          date = parseDate(dateStr);
        } catch (DateTimeParseException ignored) {
          // Continue with creating a new entity
        }
      }
    }

    milkQuality = MilkQuality.builder()
        .centreId(centreId)
        .date(date)
        .programId(program.getProgramId())
        .partnerId(partner.getPartnerId())
        .excelFileMetaData(excelFileMetaData)
        .build();


    // Map Excel columns to entity fields
    mapExcelColumnsToFields(milkQuality, MilkQuality.class, mappings, columnValues, program, partner);

    return milkQuality;
  }
}
