package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.PartnerRepository;
import org.technoserve.udp.repository.ProgramRepository;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ExcelExportService {

    private final CentreRepository centreRepository;
    private final PartnerRepository partnerRepository;
    private final ProgramRepository programRepository;

    private final MasterReportService masterReportService;
    private final TransactionalReportService transactionalReportService;

    /**
     * Generate centre report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCentreReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all centre data without pagination for Excel export
        List<CentreReportDto> centreData = (List<CentreReportDto>) masterReportService.generateCentreReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Centre Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Centre ID", "Centre Name", "Centre Type", "Route No", "Route Name",
                "Facilitator Name", "Facilitator ID", "Facilitator Country Code", "Facilitator Mobile Number",
                "State", "District", "Taluk", "Village", "Latitude", "Longitude",
                "Installed Capacity", "ICS Type", "Partner Name", "Program Name"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (CentreReportDto centre : centreData) {
                Row row = sheet.createRow(rowNum++);

                setCellValue(row, 0, centre.getCentreId(), dataStyle);
                setCellValue(row, 1, centre.getCentreName(), dataStyle);
                setCellValue(row, 2, centre.getCentreType(), dataStyle);
                setCellValue(row, 3, centre.getRouteNo(), dataStyle);
                setCellValue(row, 4, centre.getRouteName(), dataStyle);
                setCellValue(row, 5, centre.getFacilitatorName(), dataStyle);
                setCellValue(row, 6, centre.getFacilitatorId(), dataStyle);
                setCellValue(row, 7, centre.getFacilitatorCountryCode(), dataStyle);
                setCellValue(row, 8, centre.getFacilitatorMobileNumber(), dataStyle);
                setCellValue(row, 9, centre.getState(), dataStyle);
                setCellValue(row, 10, centre.getDistrict(), dataStyle);
                setCellValue(row, 11, centre.getTaluk(), dataStyle);
                setCellValue(row, 12, centre.getVillage(), dataStyle);
                setCellValue(row, 13, centre.getLatitude(), dataStyle);
                setCellValue(row, 14, centre.getLongitude(), dataStyle);
                setCellValue(row, 15, centre.getInstalledCapacity(), dataStyle);
                setCellValue(row, 16, centre.getIcsType(), dataStyle);
                setCellValue(row, 17, centre.getPartnerName(), dataStyle);
                setCellValue(row, 18, centre.getProgramName(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate farmer report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateFarmerReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all farmer data without pagination for Excel export
        List<FarmerReportDto> farmerData = (List<FarmerReportDto>) masterReportService.generateFarmerReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Farmer Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Farmer ID", "Farmer Tracenet Code", "Farmer Name", "Age", "Gender",
                "State", "District", "Village", "Country Code", "Mobile Number",
                "Centre ID", "Centre Name", "Centre Type", "Marital Status", "Spouse Name",
                "Caste", "Highest Education", "House Hold Size", "Land Size Under Cultivation", "Land Measure Type",
                "Organic Status", "Herd Size", "Any Other Income Generating Activity", "Household Annual Income", "Agricultural Annual Income",
                "Dairy Annual Income", "Other Annual Income", "Crops Grown", "Cattle Breed Types", "Loan Amount",
                "Agricultural Loan", "Dairy Loan", "Partner Name", "Program Name", "Lat Long"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (FarmerReportDto farmer : farmerData) {
                Row row = sheet.createRow(rowNum++);

                setCellValue(row, 0, farmer.getFarmerId(), dataStyle);
                setCellValue(row, 1, farmer.getFarmerTracenetCode(), dataStyle);
                setCellValue(row, 2, farmer.getFarmerName(), dataStyle);
                setCellValue(row, 3, farmer.getAge(), dataStyle);
                setCellValue(row, 4, farmer.getGender(), dataStyle);
                setCellValue(row, 5, farmer.getState(), dataStyle);
                setCellValue(row, 6, farmer.getDistrict(), dataStyle);
                setCellValue(row, 7, farmer.getVillage(), dataStyle);
                setCellValue(row, 8, farmer.getCountryCode(), dataStyle);
                setCellValue(row, 9, farmer.getMobileNumber(), dataStyle);
                setCellValue(row, 10, farmer.getCentreId(), dataStyle);
                setCellValue(row, 11, farmer.getCentreName(), dataStyle);
                setCellValue(row, 12, farmer.getCentreType(), dataStyle);
                setCellValue(row, 13, farmer.getMaritalStatus(), dataStyle);
                setCellValue(row, 14, farmer.getSpouseName(), dataStyle);
                setCellValue(row, 15, farmer.getCaste(), dataStyle);
                setCellValue(row, 16, farmer.getHighestEducation(), dataStyle);
                setCellValue(row, 17, farmer.getHouseHoldSize(), dataStyle);
                setCellValue(row, 18, farmer.getLandSizeUnderCultivation(), dataStyle);
                setCellValue(row, 19, farmer.getLandMeasureType(), dataStyle);
                setCellValue(row, 20, farmer.getOrganicStatus(), dataStyle);
                setCellValue(row, 21, farmer.getHerdSize(), dataStyle);
                setCellValue(row, 22, farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
                setCellValue(row, 23, farmer.getHouseholdAnnualIncome(), dataStyle);
                setCellValue(row, 24, farmer.getAgriculturalAnnualIncome(), dataStyle);
                setCellValue(row, 25, farmer.getDairyAnnualIncome(), dataStyle);
                setCellValue(row, 26, farmer.getOtherAnnualIncome(), dataStyle);
                setCellValue(row, 27, farmer.getCropsGrown(), dataStyle);
                setCellValue(row, 28, farmer.getCattleBreedTypes(), dataStyle);
                setCellValue(row, 29, farmer.getLoanAmount(), dataStyle);
                setCellValue(row, 30, farmer.getAgriculturalLoan(), dataStyle);
                setCellValue(row, 31, farmer.getDairyLoan(), dataStyle);
                setCellValue(row, 32, farmer.getPartnerName(), dataStyle);
                setCellValue(row, 33, farmer.getProgramName(), dataStyle);
                setCellValue(row, 34, farmer.getLatLong(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate staff report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateStaffReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all staff data without pagination for Excel export
        List<StaffReportDto> staffData = (List<StaffReportDto>) masterReportService.generateStaffReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Staff Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Staff ID", "Name", "Designation", "Gender", "Country Code",
                "Mobile Number", "Centre ID", "Centre Name", "Centre Type", "District",
                "State", "Partner Name", "Program Name"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (StaffReportDto staff : staffData) {
                Row row = sheet.createRow(rowNum++);

                setCellValue(row, 0, staff.getStaffId(), dataStyle);
                setCellValue(row, 1, staff.getName(), dataStyle);
                setCellValue(row, 2, staff.getDesignation(), dataStyle);
                setCellValue(row, 3, staff.getGender(), dataStyle);
                setCellValue(row, 4, staff.getCountryCode(), dataStyle);
                setCellValue(row, 5, staff.getMobileNumber(), dataStyle);
                setCellValue(row, 6, staff.getCentreId(), dataStyle);
                setCellValue(row, 7, staff.getCentreName(), dataStyle);
                setCellValue(row, 8, staff.getCentreType(), dataStyle);
                setCellValue(row, 9, staff.getDistrict(), dataStyle);
                setCellValue(row, 10, staff.getState(), dataStyle);
                setCellValue(row, 11, staff.getPartnerName(), dataStyle);
                setCellValue(row, 12, staff.getProgramName(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate dairy output report as Excel file for download
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param startDate The start date to filter by (optional)
     * @param endDate The end date to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateDairyOutputReportExcel(Long partnerId, Long programId, LocalDate startDate, LocalDate endDate, String sortBy, String sortDir) throws IOException {
        // Get all dairy output data without pagination for Excel export
        List<DairyOutputReportDto> dairyData = (List<DairyOutputReportDto>) transactionalReportService.generateDairyOutputReport(partnerId, programId, startDate, endDate, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Dairy Output Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Partner Name", "Centre Type", "Centre Name", "Centre ID", "Installed Capacity",
                "Total Milk Volume", "Milk Received LPD", "Utilization Percentage", "Average Fat", "Average SNF",
                "Segregated Milk Volume", "AB Positive Milk Volume", "AFM1 Positive Milk Volume", "High Sodium Milk Volume", "Overall Positive Milk Volume",
                "Overall Negative Milk Volume", "QBI Volume Milk Volume", "Compliant Percentage", "AB Pos Percentage", "AFM1 Pos Percentage",
                "Beta Pos Volume", "Beta Pos Percentage", "Sulfa Pos Volume", "Sulfa Pos Percentage", "Tetra Pos Volume",
                "Tetra Pos Percentage", "Cap Pos Volume", "Cap Pos Percentage"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (DairyOutputReportDto dairy : dairyData) {
                Row row = sheet.createRow(rowNum++);

                setCellValue(row, 0, dairy.getPartnerName(), dataStyle);
                setCellValue(row, 1, dairy.getCentreType(), dataStyle);
                setCellValue(row, 2, dairy.getCentreName(), dataStyle);
                setCellValue(row, 3, dairy.getCentreId(), dataStyle);
                setCellValue(row, 4, dairy.getInstalledCapacity(), dataStyle);
                setCellValue(row, 5, dairy.getTotalMilkVolume(), dataStyle);
                setCellValue(row, 6, dairy.getMilkReceivedLPD(), dataStyle);
                setCellValue(row, 7, dairy.getUtilizationPercentage(), dataStyle);
                setCellValue(row, 8, dairy.getAverageFat(), dataStyle);
                setCellValue(row, 9, dairy.getAverageSNF(), dataStyle);
                setCellValue(row, 10, dairy.getSegregatedMilkVolume(), dataStyle);
                setCellValue(row, 11, dairy.getAbPositiveMilkVolume(), dataStyle);
                setCellValue(row, 12, dairy.getAfm1PositiveMilkVolume(), dataStyle);
                setCellValue(row, 13, dairy.getHighSodiumMilkVolume(), dataStyle);
                setCellValue(row, 14, dairy.getOverallPositiveMilkVolume(), dataStyle);
                setCellValue(row, 15, dairy.getOverallNegativeMilkVolume(), dataStyle);
                setCellValue(row, 16, dairy.getQbiVolumeMilkVolume(), dataStyle);
                setCellValue(row, 17, dairy.getCompliantPercentage(), dataStyle);
                setCellValue(row, 18, dairy.getAbPosPercentage(), dataStyle);
                setCellValue(row, 19, dairy.getAfm1PosPercentage(), dataStyle);
                setCellValue(row, 20, dairy.getBetaPosVolume(), dataStyle);
                setCellValue(row, 21, dairy.getBetaPosPercentage(), dataStyle);
                setCellValue(row, 22, dairy.getSulfaPosVolume(), dataStyle);
                setCellValue(row, 23, dairy.getSulfaPosPercentage(), dataStyle);
                setCellValue(row, 24, dairy.getTetraPosVolume(), dataStyle);
                setCellValue(row, 25, dairy.getTetraPosPercentage(), dataStyle);
                setCellValue(row, 26, dairy.getCapPosVolume(), dataStyle);
                setCellValue(row, 27, dairy.getCapPosPercentage(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate cotton farming report as Excel file for download
     * This creates a flattened Excel with farmer data and all year data in separate columns
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param years The list of years to filter by (required)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCottonFarmingReportExcel(Long partnerId, Long programId, List<Integer> years, String sortBy, String sortDir) throws IOException {

    }

    /**
     * Helper method to create header style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        return headerStyle;
    }

    /**
     * Helper method to create data style
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        return dataStyle;
    }

    /**
     * Helper method to set cell value with proper type handling
     */
    private void setCellValue(Row row, int columnIndex, Object value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellStyle(style);

        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }
}
