package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.MilkQuality;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.MilkQualityRepository;
import org.technoserve.udp.util.UdpConstants;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Validator for MilkQuality entities
 */
public class MilkQualityValidator extends AbstractEntityValidator<MilkQuality> {

    private final CentreRepository centreRepository;

    private final MilkQualityRepository milkQualityRepository;

    public MilkQualityValidator(CentreRepository centreRepository, MilkQualityRepository milkQualityRepository) {
        this.centreRepository = centreRepository;
        this.milkQualityRepository = milkQualityRepository;
    }

    @Override
    public void validate(MilkQuality milkQuality, Map<String, String> validationErrors) {
        // Validate required fields
        if (milkQuality.getCentreId() == null || milkQuality.getCentreId().isEmpty()) {
            validationErrors.put("Centre ID", "Centre ID is required");
        }

        if (milkQuality.getDate() == null) {
            validationErrors.put("Date", "Date is required");
        }

        if (milkQuality.getTotalMilkVolume() == null) {
            validationErrors.put("Total Milk Volume", "Total Milk Volume is required");
        }

        if (milkQuality.getFat() == null) {
            validationErrors.put("FAT", "FAT is required");
        }

        if (milkQuality.getSnf() == null) {
            validationErrors.put("SNF", "SNF is required");
        }

        if (milkQuality.getSegregatedMilkVolume() == null) {
            validationErrors.put("Segregated Milk Volume", "Segregated Milk Volume is required");
        }

        // Validate that the Centre ID exists in the database
        if (milkQuality.getCentreId() != null && !milkQuality.getCentreId().isEmpty()) {
            boolean centreExists = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
                    milkQuality.getCentreId(), milkQuality.getProgramId(), milkQuality.getPartnerId()).isPresent();
            if (!centreExists) {
                validationErrors.put("Centre ID", "Centre ID does not exist in the database");
            }
        }

    }

    @Override
    public void performCustomValidations(MilkQuality milkQuality, Map<String, String> validationErrors) {
        // Add any custom validations specific to MilkQuality
        boolean exists = milkQualityRepository.existsByCentreIdAndDateAndProgramIdAndPartnerId(
                milkQuality.getCentreId(), milkQuality.getDate(), milkQuality.getProgramId(), milkQuality.getPartnerId());

        if (exists) {
            validationErrors.put("Date", "Data already exists for this centre and date");
        }

    }

    @Override
    public boolean isRequiredField(String fieldName) {
        return UdpConstants.MILK_QUALITY_REQUIRED_FIELDS.contains(fieldName);
    }

    @Override
    public Set<String> getRequiredFields() {
        return UdpConstants.MILK_QUALITY_REQUIRED_FIELDS;
    }
}
