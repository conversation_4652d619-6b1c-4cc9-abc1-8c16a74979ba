package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.DairyFieldData;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.DairyFieldDataRepository;
import org.technoserve.udp.service.processor.validator.DairyFieldDataValidator;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Processor for Dairy Field Data
 */
public class DairyFieldDataProcessor extends AbstractDataProcessor<DairyFieldData> {

    private final DairyFieldDataRepository dairyFieldDataRepository;

    public DairyFieldDataProcessor(DairyFieldDataRepository dairyFieldDataRepository, FormulaEvaluator evaluator) {
        super(new DairyFieldDataValidator(),evaluator);
        this.dairyFieldDataRepository = dairyFieldDataRepository;
    }

    @Override
    protected DairyFieldData createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
        return DairyFieldData.builder()
                .programId(program.getProgramId())
                .partnerId(partner.getPartnerId())
                .excelFileMetaData(excelFileMetaData)
                .build();
    }

    @Override
    protected void processIdField(Row row, Map<Integer, String> columnIndices, DairyFieldData entity, Map<String, String> validationErrors, Map<String, String> mappings) {
        // Process Date field
        String dateExcelColumn = mappings.get("date");
        Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

        String dateStr = columnValues.get(dateExcelColumn);
        if (dateStr == null || dateStr.isEmpty()) {
            validationErrors.put("Date", "Date is required");
        } else {
            try {
                // Try different date formats
                LocalDate date = parseDate(dateStr);
                entity.setDate(date);
            } catch (DateTimeParseException e) {
                validationErrors.put("Date", "Invalid date format: " + dateStr);
            }
        }

    }


    @Override
    protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices, Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
        List<DairyFieldData> dairyFieldDataToSave = new ArrayList<>();
        int batchRecordsProcessed = 0;

        // Process each row in the batch
        for (int i = startRow; i <= endRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // Process the row and get the dairy field data entity
            DairyFieldData dairyFieldData = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);

            // Add to batch if row has data
            if (dairyFieldData != null) {
                dairyFieldDataToSave.add(dairyFieldData);
                batchRecordsProcessed++;
            }
        }

        // Save all entities in the batch
        if (!dairyFieldDataToSave.isEmpty()) {
            dairyFieldDataRepository.saveAll(dairyFieldDataToSave);
        }

        return batchRecordsProcessed;
    }

    /**
     * Process a single row and return the dairy field data entity
     *
     * @param row           The Excel row
     * @param columnIndices Map of column indices to column names
     * @param mappings      Map of entity field names to Excel column names
     * @param program       The program
     * @param partner       The partner
     * @return The dairy field data entity, or null if the row has no data
     */
    private DairyFieldData processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings,
                                     Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
        // Create a new DairyFieldData object or find an existing one by date if available
        DairyFieldData dairyFieldData = null;
        LocalDate date = null;

        // First, collect all values from the row into a HashMap
        Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

        // Check if the row has any data
        if (columnValues.isEmpty()) {
            return null;
        }

        // Get date from the row
        String dateExcelColumn = mappings.get("date");

        if (dateExcelColumn != null) {
            String dateStr = columnValues.get(dateExcelColumn);

            if (dateStr != null && !dateStr.isEmpty()) {
                try {
                    date = parseDate(dateStr);
                    
                    // Try to find an existing dairy field data with this date, programId, and partnerId
                    Optional<DairyFieldData> existingDairyFieldData = dairyFieldDataRepository
                            .findByDateAndProgramIdAndPartnerId(date, program.getProgramId(), partner.getPartnerId());
                    if (existingDairyFieldData.isPresent()) {
                        dairyFieldData = existingDairyFieldData.get();
                    }
                } catch (DateTimeParseException ignored) {
                    // Invalid date format, will be caught by validation
                }
            }
        }

        // If no existing dairy field data found, create a new one
        if (dairyFieldData == null) {
            dairyFieldData = DairyFieldData.builder()
                    .date(date)
                    .programId(program.getProgramId())
                    .partnerId(partner.getPartnerId())
                    .excelFileMetaData(excelFileMetaData)
                    .build();
        }

        // Map Excel columns to entity fields
        mapExcelColumnsToFields(dairyFieldData, DairyFieldData.class, mappings, columnValues, program, partner);

        return dairyFieldData;
    }
}
