package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CottonFarmingDataRepository;
import org.technoserve.udp.repository.FarmerRepository;
import org.technoserve.udp.service.processor.validator.CottonFarmingDataValidator;
import org.technoserve.udp.util.UdpCommonUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Processor for Cotton Farming Data
 */
public class CottonFarmingDataProcessor extends AbstractDataProcessor<CottonFarmingData> {

    private final CottonFarmingDataRepository cottonFarmingDataRepository;
    private final FarmerRepository farmerRepository;

    public CottonFarmingDataProcessor(CottonFarmingDataRepository cottonFarmingDataRepository, 
                                     FarmerRepository farmerRepository,
                                     FormulaEvaluator evaluator) {
        super(new CottonFarmingDataValidator(farmerRepository, cottonFarmingDataRepository), evaluator);
        this.cottonFarmingDataRepository = cottonFarmingDataRepository;
        this.farmerRepository = farmerRepository;
    }

    @Override
    protected CottonFarmingData createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
        return CottonFarmingData.builder()
                .programId(program.getProgramId())
                .partnerId(partner.getPartnerId())
                .excelFileMetaData(excelFileMetaData)
                .build();
    }

    @Override
    protected void processIdField(Row row, Map<Integer, String> columnIndices, CottonFarmingData entity, 
                                 Map<String, String> validationErrors, Map<String, String> mappings) {
        // Check if we have a Farmer ID
        String farmerId = null;
        String farmerIdExcelColumn = mappings.get("farmerId");
        Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

        farmerId = columnValues.get(farmerIdExcelColumn);
        if (farmerId == null || farmerId.isEmpty()) {
            validationErrors.put("Farmer ID", "Farmer ID is required");
        } else {
            entity.setFarmerId(farmerId);
        }
        
        // Process Year field if it's part of the composite key
        String yearExcelColumn = mappings.get("year");
        if (yearExcelColumn != null) {
            String yearValue = columnValues.get(yearExcelColumn);
            if (yearValue != null && !yearValue.isEmpty()) {
                try {
                    entity.setYear(Integer.parseInt(yearValue));
                } catch (NumberFormatException e) {
                    validationErrors.put("Year", "Year must be a valid number");
                }
            }
        }
    }

    @Override
    protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices, 
                              Map<String, String> mappings, Program program, Partner partner, 
                              ExcelFileMetaData excelFileMetaData) {
        List<CottonFarmingData> cottonFarmingDataToSave = new ArrayList<>();
        int batchRecordsProcessed = 0;

        // Process each row in the batch
        for (int i = startRow; i <= endRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // Process the row and get the cotton farming data entity
            CottonFarmingData cottonFarmingData = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);

            prepareForSave(cottonFarmingData);

            cottonFarmingDataToSave.add(cottonFarmingData);
            batchRecordsProcessed++;
        }

        // Save all entities in one batch
        if (!cottonFarmingDataToSave.isEmpty()) {
            cottonFarmingDataRepository.saveAll(cottonFarmingDataToSave);
        }

        return batchRecordsProcessed;
    }

    /**
     * Process a single row and return the cotton farming data entity
     */
    private CottonFarmingData processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings, 
                                        Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
        // Create a new entity
        CottonFarmingData cottonFarmingData = createEntity(program, partner, excelFileMetaData);

        // Map of validation errors for this row
        Map<String, String> validationErrors = new java.util.HashMap<>();

        // Process ID field
        processIdField(row, columnIndices, cottonFarmingData, validationErrors, mappings);

        // First, collect all values from the row into a HashMap
        Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);



            cottonFarmingData.setFarmerId(cottonFarmingData.getFarmerId());
            cottonFarmingData.setProgramId(cottonFarmingData.getProgramId());
            cottonFarmingData.setPartnerId(cottonFarmingData.getPartnerId());
            cottonFarmingData.setYear(cottonFarmingData.getYear());


        mapExcelColumnsToFields(cottonFarmingData, CottonFarmingData.class, mappings, columnValues, program, partner);

        return cottonFarmingData;
    }


    private void prepareForSave(CottonFarmingData cottonFarmingData) {

        cottonFarmingData.setIrrigationSource(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getIrrigationSource()));
        cottonFarmingData.setIrrigationMethod(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getIrrigationMethod()));

        cottonFarmingData.setHasStorageSpace(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getHasStorageSpace()));
        cottonFarmingData.setReceivesAgroAdvisory(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getReceivesAgroAdvisory()));
        cottonFarmingData.setReceivedTraining(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getReceivedTraining()));
        cottonFarmingData.setMembershipInOrg(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getMembershipInOrg()));
        cottonFarmingData.setMaintainsRecords(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getMaintainsRecords()));
        cottonFarmingData.setFarmMachineryHired(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getFarmMachineryHired()));
        cottonFarmingData.setMulchingUsed(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getMulchingUsed()));
        cottonFarmingData.setSafetyKitForWorkers(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getSafetyKitForWorkers()));
        cottonFarmingData.setShelterAndWaterForWorkers(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getShelterAndWaterForWorkers()));
        cottonFarmingData.setLavatoryForWorkers(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getLavatoryForWorkers()));
        cottonFarmingData.setWomenInAgriOperations(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getWomenInAgriOperations()));
        cottonFarmingData.setCommunityWaterHarvesting(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getCommunityWaterHarvesting()));
        cottonFarmingData.setSoilMoistureMeterUsed(UdpCommonUtil.toBeginningUpperCase(cottonFarmingData.getSoilMoistureMeterUsed()));


        // Pre processing
        calculateTotalHHMembers(cottonFarmingData);
        calculateDependencyRatio(cottonFarmingData);
        calculateGenderRatio(cottonFarmingData);
        calculateSchoolAttendanceRate(cottonFarmingData);
        calculateTotalCottonLand(cottonFarmingData);
        calculateOrganicPercent(cottonFarmingData);
        calculateLandUsedForCotton(cottonFarmingData);
        calculateIncomePerEarner(cottonFarmingData);
        calculateOCIncome(cottonFarmingData);
        calculateProfitPerAcre(cottonFarmingData);
        calculateTotalCertificationCost(cottonFarmingData);
        calculateTotalPTCost(cottonFarmingData);
        calculateTotalYSTCost(cottonFarmingData);
        calculateTotalBSTCost(cottonFarmingData);
        calculateTotalPestMgmtCost(cottonFarmingData);
        calculateTotalLabourCost(cottonFarmingData);
        calculateMachineryCostTotal(cottonFarmingData);
        calculateTotalIrrigationCost(cottonFarmingData);
        calculateIrrigationFrequency(cottonFarmingData);
    }

    private void calculateTotalHHMembers(CottonFarmingData cottonFarmingData) {
        if (cottonFarmingData.getMalesInHousehold() == null) {
            cottonFarmingData.setMalesInHousehold(0);
        }
        if (cottonFarmingData.getFemalesInHousehold() == null) {
            cottonFarmingData.setFemalesInHousehold(0);
        }
        if (cottonFarmingData.getChildrenInHousehold() == null) {
            cottonFarmingData.setChildrenInHousehold(0);
        }
        cottonFarmingData.setTotalHHMembers(cottonFarmingData.getMalesInHousehold() + cottonFarmingData.getFemalesInHousehold() + cottonFarmingData.getChildrenInHousehold());

    }


    private void calculateDependencyRatio(CottonFarmingData cottonFarmingData) {
        Integer children = cottonFarmingData.getChildrenInHousehold();
        Integer earners = cottonFarmingData.getEarningMembers();

        if (earners != null && earners > 0 && children != null) {
            double ratio = (double) children / earners;
            BigDecimal roundedRatio = BigDecimal.valueOf(ratio)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setDependencyRatio(roundedRatio.doubleValue());
        } else {
            cottonFarmingData.setDependencyRatio(0.0); // or handle as needed
        } }

    private void calculateGenderRatio(CottonFarmingData cottonFarmingData) {
        Integer males = cottonFarmingData.getMalesInHousehold();
        Integer females = cottonFarmingData.getFemalesInHousehold();

        if (males != null && females != null && females > 0) {
            double ratio = (double) males / females;
            BigDecimal roundedRatio = BigDecimal.valueOf(ratio)
                .setScale(2, RoundingMode.HALF_UP); // round to 2 decimal places
            cottonFarmingData.setGenderRatio(roundedRatio.doubleValue());
        } else {
            cottonFarmingData.setGenderRatio(0.0); // or handle as needed
        }
    }

    private void calculateSchoolAttendanceRate(CottonFarmingData cottonFarmingData) {

        Integer children = cottonFarmingData.getChildrenInHousehold();
        Integer schoolGoingChildren = cottonFarmingData.getSchoolGoingChildren();

        if (children != null && children > 0 && schoolGoingChildren != null) {
            double rate = (double) schoolGoingChildren / children;
            BigDecimal roundedRate = BigDecimal.valueOf(rate)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setSchoolAttendanceRate(roundedRate.doubleValue());
        } else {
            cottonFarmingData.setSchoolAttendanceRate(0.0); // or handle as needed
        }
    }

    private void calculateTotalCottonLand(CottonFarmingData cottonFarmingData) {
        Double nonOrganicCottonLand = cottonFarmingData.getNonOrganicCottonLand();
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();

        if (nonOrganicCottonLand != null && organicCottonLand != null) {
            double totalCottonLand = nonOrganicCottonLand + organicCottonLand;
            BigDecimal roundedTotal = BigDecimal.valueOf(totalCottonLand)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalCottonLand(roundedTotal.doubleValue());
        } else {
            cottonFarmingData.setTotalCottonLand(0.0); // or handle as needed
        }
    }

    private void calculateOrganicPercent(CottonFarmingData cottonFarmingData) {
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();
        Double totalLandholding = cottonFarmingData.getTotalLandholding();

        if (organicCottonLand != null && totalLandholding != null && totalLandholding > 0) {
            double organicPercent = (organicCottonLand / totalLandholding);
            BigDecimal roundedPercent = BigDecimal.valueOf(organicPercent)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setOrganicPercent(roundedPercent.doubleValue());
        } else {
            cottonFarmingData.setOrganicPercent(0.0); // or handle as needed
        }

    }

    private void calculateLandUsedForCotton(CottonFarmingData cottonFarmingData) {
        Double totalNonOrganicCottonLand = cottonFarmingData.getNonOrganicCottonLand();
        Double totaOrganicCottonLand = cottonFarmingData.getOrganicCottonLand();
        Double totalLandholding = cottonFarmingData.getTotalLandholding();
        if (totalNonOrganicCottonLand != null && totaOrganicCottonLand != null && totalLandholding != null && totalLandholding > 0) {
            double landUsedForCotton = (totalNonOrganicCottonLand + totaOrganicCottonLand) / totalLandholding;
            BigDecimal roundedValue = BigDecimal.valueOf(landUsedForCotton)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setLandUsedForCotton(roundedValue.doubleValue());
        } else {
            cottonFarmingData.setLandUsedForCotton(0.0); // or handle as needed
        }
    }

    private void calculateIncomePerEarner(CottonFarmingData cottonFarmingData) {
        BigDecimal annualHouseholdIncome = cottonFarmingData.getAnnualHouseholdIncome();
        Integer earningMembers = cottonFarmingData.getEarningMembers();

        if (annualHouseholdIncome != null && earningMembers != null && earningMembers > 0) {
            double incomePerEarner = annualHouseholdIncome.doubleValue() / earningMembers;
            BigDecimal roundedIncome = BigDecimal.valueOf(incomePerEarner)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setIncomePerEarner(roundedIncome.doubleValue());
        } else {
            cottonFarmingData.setIncomePerEarner(0.0); // or handle as needed
        }
    }

    private void calculateOCIncome(CottonFarmingData cottonFarmingData) {
        Double organicCottonQuantitySold = cottonFarmingData.getOrganicCottonQuantitySold();
        BigDecimal sellingPricePerKg = cottonFarmingData.getSellingPricePerKg();

        if (organicCottonQuantitySold != null && sellingPricePerKg != null) {
            double ocIncome = organicCottonQuantitySold * sellingPricePerKg.doubleValue();
            BigDecimal roundedIncome = BigDecimal.valueOf(ocIncome)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setOcIncome(roundedIncome.doubleValue());
        } else {
            cottonFarmingData.setOcIncome(0.0); // or handle as needed
        }
    }

    private void calculateProfitPerAcre(CottonFarmingData cottonFarmingData) {

        BigDecimal costOfCultivationPerAcre = cottonFarmingData.getCostOfCultivationPerAcre();
        BigDecimal sellingPricePerKg = cottonFarmingData.getSellingPricePerKg();
        Double avgProductionPerAcre = cottonFarmingData.getAvgProductionPerAcre();

        if (costOfCultivationPerAcre != null && sellingPricePerKg != null && avgProductionPerAcre != null) {
            double profitPerAcre = (sellingPricePerKg.doubleValue() * avgProductionPerAcre) - costOfCultivationPerAcre.doubleValue();
            BigDecimal roundedProfit = BigDecimal.valueOf(profitPerAcre)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setProfitPerAcre(roundedProfit.doubleValue());
        } else {
            cottonFarmingData.setProfitPerAcre(0.0); // or handle as needed
        }
    }

    private void calculateTotalCertificationCost(CottonFarmingData cottonFarmingData) {
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();
        BigDecimal certificationCostPerAcre = cottonFarmingData.getCertificationCostPerAcre();

        if (organicCottonLand != null && certificationCostPerAcre != null) {
            double totalCertificationCost = organicCottonLand * certificationCostPerAcre.doubleValue();
            BigDecimal roundedCost = BigDecimal.valueOf(totalCertificationCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalCertificationCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalCertificationCost(0.0); // or handle as needed
        }
    }
    private void calculateTotalPTCost(CottonFarmingData cottonFarmingData) {

        Integer pheromoneTrapsPerAcre = cottonFarmingData.getPheromoneTrapsPerAcre();
        BigDecimal pheromoneTrapsPrice = cottonFarmingData.getPheromoneTrapsPrice();

        if (pheromoneTrapsPerAcre != null && pheromoneTrapsPrice != null) {
            double totalPTCost = pheromoneTrapsPerAcre * pheromoneTrapsPrice.doubleValue();
            BigDecimal roundedCost = BigDecimal.valueOf(totalPTCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalPTCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalPTCost(0.0); // or handle as needed
        }

    }

    private void calculateTotalYSTCost(CottonFarmingData cottonFarmingData) {
        Integer yellowStickyTrapsPerAcre = cottonFarmingData.getYellowStickyTrapsPerAcre();
        BigDecimal yellowStickyTrapsPrice = cottonFarmingData.getYellowStickyTrapsPrice();

        if (yellowStickyTrapsPerAcre != null && yellowStickyTrapsPrice != null) {
            double totalYSTCost = yellowStickyTrapsPerAcre * yellowStickyTrapsPrice.doubleValue();
            BigDecimal roundedCost = BigDecimal.valueOf(totalYSTCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalYSTCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalYSTCost(0.0); // or handle as needed
        }
    }

    private void calculateTotalBSTCost(CottonFarmingData cottonFarmingData) {
        Integer blueStickyTrapsPerAcre = cottonFarmingData.getBlueStickyTrapsPerAcre();
        BigDecimal blueStickyTrapsPrice = cottonFarmingData.getBlueStickyTrapsPrice();

        if (blueStickyTrapsPerAcre != null && blueStickyTrapsPrice != null) {
            double totalBSTCost = blueStickyTrapsPerAcre * blueStickyTrapsPrice.doubleValue();
            BigDecimal roundedCost = BigDecimal.valueOf(totalBSTCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalBSTCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalBSTCost(0.0); // or handle as needed
        }
    }

    private void calculateTotalPestMgmtCost(CottonFarmingData cottonFarmingData) {
        Double totalPTCost = cottonFarmingData.getTotalPTCost();
        Double totalYSTCost = cottonFarmingData.getTotalYSTCost();
        Double totalBSTCost = cottonFarmingData.getTotalBSTCost();
        BigDecimal bioInputsCost = cottonFarmingData.getBioInputsCost();
        if (totalPTCost != null && totalYSTCost != null && totalBSTCost != null && bioInputsCost != null) {
            double totalPestMgmtCost = totalPTCost + totalYSTCost + totalBSTCost + bioInputsCost.doubleValue();
            BigDecimal roundedCost = BigDecimal.valueOf(totalPestMgmtCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalPestMgmtCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalPestMgmtCost(0.0); // or handle as needed
        }
    }

    private void calculateTotalLabourCost(CottonFarmingData cottonFarmingData) {
        Integer workersForSowing = cottonFarmingData.getWorkersForSowing();
        Integer workersForHarvesting = cottonFarmingData.getWorkersForHarvesting();
        BigDecimal localLabourCostPerDay = cottonFarmingData.getLocalLabourCostPerDay();
        BigDecimal migrantLabourCostPerDay = cottonFarmingData.getMigrantLabourCostPerDay();

        if (workersForSowing != null && workersForHarvesting != null && localLabourCostPerDay != null && migrantLabourCostPerDay != null && (workersForSowing > 0
            || workersForHarvesting > 0) && (localLabourCostPerDay.doubleValue() > 0 || migrantLabourCostPerDay.doubleValue() > 0)) {
            double totalLabourCost = (workersForSowing + workersForHarvesting) * ((localLabourCostPerDay.doubleValue() + migrantLabourCostPerDay.doubleValue()) / 2);
            BigDecimal roundedCost = BigDecimal.valueOf(totalLabourCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalLabourCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalLabourCost(0.0); // or handle as needed
        }
    }

    private void calculateMachineryCostTotal(CottonFarmingData cottonFarmingData) {
        if (!"yes".equalsIgnoreCase(cottonFarmingData.getFarmMachineryHired())) {
            cottonFarmingData.setMachineryCostTotal(0.0);
            return;
        }

        BigDecimal machineryHiringCost = cottonFarmingData.getMachineryHiringCost();
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();

        if (machineryHiringCost != null && organicCottonLand != null) {
            double totalCost = machineryHiringCost.doubleValue() * organicCottonLand;
            BigDecimal roundedCost = BigDecimal.valueOf(totalCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setMachineryCostTotal(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setMachineryCostTotal(0.0); // or null if appropriate
        }
    }

    private void calculateTotalIrrigationCost(CottonFarmingData cottonFarmingData) {
        BigDecimal irrigationCostPerAcre = cottonFarmingData.getIrrigationCostPerAcre();
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();

        if (irrigationCostPerAcre != null && organicCottonLand != null) {
            double totalCost = irrigationCostPerAcre.doubleValue() * organicCottonLand;
            BigDecimal roundedCost = BigDecimal.valueOf(totalCost)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setTotalIrrigationCost(roundedCost.doubleValue());
        } else {
            cottonFarmingData.setTotalIrrigationCost(0.0); // or null if appropriate
        }
    }
    private void calculateIrrigationFrequency(CottonFarmingData cottonFarmingData) {
        Double organicCottonLand = cottonFarmingData.getOrganicCottonLand();
        Integer irrigationCount = cottonFarmingData.getIrrigationCount();

        if (organicCottonLand != null && irrigationCount != null && organicCottonLand > 0) {
            double frequency = (double) irrigationCount / organicCottonLand;
            BigDecimal roundedFrequency = BigDecimal.valueOf(frequency)
                .setScale(2, RoundingMode.HALF_UP);
            cottonFarmingData.setIrrigationFrequency(roundedFrequency.doubleValue());
        } else {
            cottonFarmingData.setIrrigationFrequency(0.0); // or null if appropriate
        }
    }


}
