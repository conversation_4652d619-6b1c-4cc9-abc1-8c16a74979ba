package org.technoserve.udp.util;

import java.util.Set;

public class UdpConstants {

  private UdpConstants() {
    throw new IllegalStateException("Utility class");
  }

  public static final String FARMER = "Farmer";
  public static final String CENTRE = "Centre";
  public static final String STAFF = "Staff";
  public static final String MILK_QUALITY = "MilkQuality";
  public static final String DAIRY_FIELD_DATA = "DairyFieldData";
  public static final String COTTON_FARMING_DATA = "CottonFarmingData";

  public static final Set<String> CENTRE_REQUIRED_FIELDS = Set.of("centreId", "centreName",
      "centreType", "facilitatorName", "facilitatorCountryCode",
      "facilitatorMobileNumber", "state", "district", "taluk",
      "village", "licenseCertificationType", "licenseCertificationStatus");

  public static final Set<String> FARMER_REQUIRED_FIELDS = Set.of(
      "farmerId","farmerName","gender",
      "state", "district", "village",
      "centreId", "countryCode", "mobileNumber", "houseHoldSize",
      "landSizeUnderCultivation", "landMeasureType", "organicStatus",
      "herdSize", "householdAnnualIncome", "agriculturalAnnualIncome",
      "dairyAnnualIncome", "cropsGrown", "cattleBreedTypes"
  );

  public static final Set<String> STAFF_REQUIRED_FIELDS = Set.of(
      "staffId",
      "designation",
      "name",
      "gender",
      "countryCode",
      "mobileNumber",
      "centreId",
      "district",
      "state"
  );

  public static final Set<String> MILK_QUALITY_REQUIRED_FIELDS = Set.of("centreId",
      "date", "totalMilkVolume", "fat", "snf", "segregatedMilkVolume"
  );

  public static final Set<String> DAIRY_FIELD_DATA_REQUIRED_FIELDS = Set.of("date", "totalFarmers", "noOfAnimalWelfareFarms", "noOfWomenEmpowerment");

  public static final Set<String> COTTON_FARMING_REQUIRED_FIELDS = Set.of(
      "farmerId", "year", "schoolGoingChildren",
      "earningMembers", "totalLandholding", "primaryCrop", "secondaryCrops",
      "nonOrganicCottonLand", "organicCottonLand", "yearsOrganicPractice", "certificationStatus",
      "irrigationSource", "cattleCount", "drinkingWaterSource", "preferredSellingPoint",
      "hasStorageSpace", "receivesAgroAdvisory", "receivedTraining", "membershipInOrg",
      "maintainsRecords", "annualHouseholdIncome", "primaryIncomeSource", "primaryIncomeAmount",
      "certificationCostPerAcre", "avgProductionPerAcre", "costOfCultivationPerAcre",
      "organicCottonQuantitySold", "sellingPricePerKg", "bioInputsCost", "pestManagementBioInputs",
      "bioFertilizerUsed", "pheromoneTrapsPerAcre", "pheromoneTrapsPrice",
      "yellowStickyTrapsPerAcre", "yellowStickyTrapsPrice",
      "blueStickyTrapsPerAcre", "blueStickyTrapsPrice",
      "birdPerchesPerAcre", "irrigationCostPerAcre", "irrigationCount", "irrigationMethod",
      "farmMachineryHired", "machineryHiringCost", "localLabourCostPerDay",
      "migrantLabourCostPerDay", "workersForSowing", "workersForHarvesting",
      "harvestingTime", "weedingMethod", "weedingCostPerAcre", "mulchingCostPerAcre",
      "tillageCount", "tillageCostPerAcre", "landPreparationCost", "organicCottonSeedRate",
      "organicCottonSeedVariety", "borderCrop", "interCrop", "coverCrop", "trapCrop",
      "mulchingUsed", "mulchingType", "storagePrecautions", "hiredVehicleForTransport",
      "transportationCostPerKg", "rejectedQuantity", "priceDiscoveryMechanism",
      "paymentTransactionType", "creditDays", "govtSchemeAvailed", "cropInsurance",
      "cropInsuranceCostPerAcre", "hasKCC", "hasActiveBankAccount", "cropRotationUsed",
      "rotationCrops", "waterTrackingDevices", "pumpCapacity", "bufferZone",
      "cropResidueUtilization", "workerPaymentMode", "wageGenderDifference",
      "labourRegister", "safetyKitForWorkers", "shelterAndWaterForWorkers",
      "lavatoryForWorkers", "womenInAgriOperations", "communityWaterHarvesting",
      "soilMoistureMeterUsed"
  );

}
