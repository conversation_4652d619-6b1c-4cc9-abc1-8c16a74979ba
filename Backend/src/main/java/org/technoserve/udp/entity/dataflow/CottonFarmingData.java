package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import jakarta.persistence.criteria.CriteriaBuilder;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;

import java.math.BigDecimal;

@Entity
@Table(name = "cotton_farming_data")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(CottonFarmingDataId.class)
public class CottonFarmingData {

  @Id
  @Column(name = "farmer_id")
  @FieldInfo(name = "Farmer ID")
  private String farmerId;

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "partner_id")
  private Long partnerId;

  @Id
  @FieldInfo(name = "Year")
  @Column(name = "year")
  private Integer year;

  @FieldInfo(name = "No. of males (adult) in household")
  @Column(name = "males_in_household")
  private Integer malesInHousehold;

  @FieldInfo(name = "No. of females (adult) in household")
  @Column(name = "females_in_household")
  private Integer femalesInHousehold;

  @FieldInfo(name = "Children (<16) in household")
  @Column(name = "children_in_household")
  private Integer childrenInHousehold;

  @FieldInfo(name = "No. of school-going children")
  @Column(name = "school_going_children")
  private Integer schoolGoingChildren;

  @FieldInfo(name = "No. of earning members in the family")
  @Column(name = "earning_members")
  private Integer earningMembers;

  @FieldInfo(name = "Total Landholding (in acres)")
  @Column(name = "total_landholding")
  private Double totalLandholding;

  @FieldInfo(name = "Primary crop")
  @Column(name = "primary_crop")
  private String primaryCrop;

  @FieldInfo(name = "Secondary crops")
  @Column(name = "secondary_crops")
  private String secondaryCrops;

  @FieldInfo(name = "Non-organic Cotton land (in acre) (if any)")
  @Column(name = "non_organic_cotton_land")
  private Double nonOrganicCottonLand;

  @FieldInfo(name = "Organic Cotton land (in acre)")
  @Column(name = "organic_cotton_land")
  private Double organicCottonLand;

  @FieldInfo(name = "Years since practicing organic cotton (#)")
  @Column(name = "years_organic_practice")
  private Integer yearsOrganicPractice;

  @FieldInfo(name = "Certification status (certified/IC1..)")
  @Column(name = "certification_status")
  private String certificationStatus;

  @FieldInfo(name = "Source of irrigation")
  @Column(name = "irrigation_source")
  private String irrigationSource;

  @FieldInfo(name = "No. of cattle (cow and Buffalo)")
  @Column(name = "cattle_count")
  private Integer cattleCount;

  @FieldInfo(name = "Source of drinking water")
  @Column(name = "drinking_water_source")
  private String drinkingWaterSource;

  @FieldInfo(name = "Preferred selling point (Aggregator/Suminter/APMC/other Gin)")
  @Column(name = "preferred_selling_point")
  private String preferredSellingPoint;

  @FieldInfo(name = "Has space for harvested cotton storage (Yes/No)")
  @Column(name = "has_storage_space")
  private String hasStorageSpace;

  @FieldInfo(name = "Receives any agro advisory (Yes/No)")
  @Column(name = "receives_agro_advisory")
  private String receivesAgroAdvisory;

  @FieldInfo(name = "Received any training on best practices for organic cotton?")
  @Column(name = "received_training")
  private String receivedTraining;

  @FieldInfo(name = "Membership in FPO/FPC/SHG")
  @Column(name = "membership_in_org")
  private String membershipInOrg;

  @FieldInfo(name = "Maintaining any Diary or Register for record keeping (Yes/No)")
  @Column(name = "maintains_records")
  private String maintainsRecords;

  @FieldInfo(name = "Annual household income(in Rs)")
  @Column(name = "annual_household_income")
  private BigDecimal annualHouseholdIncome;

  @FieldInfo(name = "Primary source of income")
  @Column(name = "primary_income_source")
  private String primaryIncomeSource;

  @FieldInfo(name = "Income from Primary source (Rs.)")
  @Column(name = "primary_income_amount")
  private BigDecimal primaryIncomeAmount;

  @FieldInfo(name = "Certification cost per annum/acre")
  @Column(name = "certification_cost_per_acre")
  private BigDecimal certificationCostPerAcre;

  @FieldInfo(name = "Avg. production of organic cotton/acre (Kg)")
  @Column(name = "avg_production_per_acre")
  private Double avgProductionPerAcre;

  @FieldInfo(name = "Cost of cultivation/acre (Rs)")
  @Column(name = "cost_of_cultivation_per_acre")
  private BigDecimal costOfCultivationPerAcre;

  @FieldInfo(name = "Quantity sold of organic cotton (in kg)")
  @Column(name = "organic_cotton_quantity_sold")
  private Double organicCottonQuantitySold;

  @FieldInfo(name = "Selling price per kg (Rs.)")
  @Column(name = "selling_price_per_kg")
  private BigDecimal sellingPricePerKg;

  @FieldInfo(name = "Material cost for bio-inputs")
  @Column(name = "bio_inputs_cost")
  private BigDecimal bioInputsCost;

  @FieldInfo(name = "Name of bio-input used for pest and disease management")
  @Column(name = "pest_management_bio_inputs")
  private String pestManagementBioInputs;

  @FieldInfo(name = "Name of bio-fertilizer/compost used")
  @Column(name = "bio_fertilizer_used")
  private String bioFertilizerUsed;

  @FieldInfo(name = "No. of pheromone traps used / acre")
  @Column(name = "pheromone_traps_per_acre")
  private Integer pheromoneTrapsPerAcre;

  @FieldInfo(name = "Cost per pheromone trap")
  @Column(name = "pheromone_traps_price")
  private BigDecimal pheromoneTrapsPrice;

  @FieldInfo(name = "No. of Yellow sticky traps used / acre")
  @Column(name = "yellow_sticky_traps_per_acre")
  private Integer yellowStickyTrapsPerAcre;

  @FieldInfo(name = "Cost per yellow sticky trap")
  @Column(name = "yellow_sticky_traps_price")
  private BigDecimal yellowStickyTrapsPrice;

  @FieldInfo(name = "No. of Blue sticky traps used / acre")
  @Column(name = "blue_sticky_traps_per_acre")
  private Integer blueStickyTrapsPerAcre;

  @FieldInfo(name = "Cost per blue sticky trap")
  @Column(name = "blue_sticky_traps_price")
  private BigDecimal blueStickyTrapsPrice;

  @FieldInfo(name = "No. of bird perches used / acre")
  @Column(name = "bird_perches_per_acre")
  private Integer birdPerchesPerAcre;

  @FieldInfo(name = "Irrigation cost/acre")
  @Column(name = "irrigation_cost_per_acre")
  private BigDecimal irrigationCostPerAcre;

  @FieldInfo(name = "No. of irrigation required for organic cotton")
  @Column(name = "irrigation_count")
  private Integer irrigationCount;

  @FieldInfo(name = "Irrigation method used")
  @Column(name = "irrigation_method")
  private String irrigationMethod;

  @FieldInfo(name = "Any farm machinery hired (Yes/No)")
  @Column(name = "farm_machinery_hired")
  private String farmMachineryHired;

  @FieldInfo(name = "Cost of machinery hiring (Rs.)/acre")
  @Column(name = "machinery_hiring_cost")
  private BigDecimal machineryHiringCost;

  @FieldInfo(name = "Local labour cost per day")
  @Column(name = "local_labour_cost_per_day")
  private BigDecimal localLabourCostPerDay;

  @FieldInfo(name = "Migrant labour cost per day")
  @Column(name = "migrant_labour_cost_per_day")
  private BigDecimal migrantLabourCostPerDay;

  @FieldInfo(name = "No. of workers required during sowing/acre")
  @Column(name = "workers_for_sowing")
  private Integer workersForSowing;

  @FieldInfo(name = "No. of workers required during harvesting/acre")
  @Column(name = "workers_for_harvesting")
  private Integer workersForHarvesting;

  @FieldInfo(name = "Harvesting time (1st, 2nd & 3rd picking) (month)")
  @Column(name = "harvesting_time")
  private String harvestingTime;

  @FieldInfo(name = "Weeding method used (manual/mechanical)")
  @Column(name = "weeding_method")
  private String weedingMethod;

  @FieldInfo(name = "Weeding cost/acre")
  @Column(name = "weeding_cost_per_acre")
  private BigDecimal weedingCostPerAcre;

  @FieldInfo(name = "Cost of mulching/acre")
  @Column(name = "mulching_cost_per_acre")
  private BigDecimal mulchingCostPerAcre;

  @FieldInfo(name = "No. of tillage practiced")
  @Column(name = "tillage_count")
  private Integer tillageCount;

  @FieldInfo(name = "Tillage cost/acre")
  @Column(name = "tillage_cost_per_acre")
  private BigDecimal tillageCostPerAcre;

  @FieldInfo(name = "Land preparation cost/acre")
  @Column(name = "land_preparation_cost")
  private BigDecimal landPreparationCost;

  @FieldInfo(name = "Seed rate of organic cotton/acre")
  @Column(name = "organic_cotton_seed_rate")
  private Double organicCottonSeedRate;

  @FieldInfo(name = "Variety of organic cotton seed (Name)")
  @Column(name = "organic_cotton_seed_variety")
  private String organicCottonSeedVariety;

  @FieldInfo(name = "Name of border crop used")
  @Column(name = "border_crop")
  private String borderCrop;

  @FieldInfo(name = "Name of the inter crop used")
  @Column(name = "inter_crop")
  private String interCrop;

  @FieldInfo(name = "Name of cover crop")
  @Column(name = "cover_crop")
  private String coverCrop;

  @FieldInfo(name = "Name of trap crop")
  @Column(name = "trap_crop")
  private String trapCrop;

  @FieldInfo(name = "Mulching used (Yes/No)")
  @Column(name = "mulching_used")
  private String mulchingUsed;

  @FieldInfo(name = "Type of mulching used (Bio-plastic/green/dry)")
  @Column(name = "mulching_type")
  private String mulchingType;

  @FieldInfo(name = "What precautions used during storage")
  @Column(name = "storage_precautions")
  private String storagePrecautions;

  @FieldInfo(name = "Hired vehicle used for transportation of seed cotton (Yes/No)")
  @Column(name = "hired_vehicle_for_transport")
  private String hiredVehicleForTransport;

  @FieldInfo(name = "Transportation cost (Rs.)/Kg of seed cotton")
  @Column(name = "transportation_cost_per_kg")
  private BigDecimal transportationCostPerKg;

  @FieldInfo(name = "Any quantity rejection due to contamination/impurities (Kg)")
  @Column(name = "rejected_quantity")
  private Double rejectedQuantity;

  @FieldInfo(name = "Price discovery mechanism")
  @Column(name = "price_discovery_mechanism")
  private String priceDiscoveryMechanism;

  @FieldInfo(name = "Payment Transaction type (Cash/online)")
  @Column(name = "payment_transaction_type")
  private String paymentTransactionType;

  @FieldInfo(name = "Days of credit after sell")
  @Column(name = "credit_days")
  private Integer creditDays;

  @FieldInfo(name = "Availing any govt. scheme or subsidy benefits (Yes/No)")
  @Column(name = "govt_scheme_availed")
  private String govtSchemeAvailed;

  @FieldInfo(name = "Opted for crop insurance (Yes/No)")
  @Column(name = "crop_insurance")
  private String cropInsurance;

  @FieldInfo(name = "Cost of crop insurance per acre")
  @Column(name = "crop_insurance_cost_per_acre")
  private BigDecimal cropInsuranceCostPerAcre;

  @FieldInfo(name = "Possess KCC (Yes/No)")
  @Column(name = "has_kcc")
  private String hasKCC;

  @FieldInfo(name = "Possess active bank account (Yes/No)")
  @Column(name = "has_active_bank_account")
  private String hasActiveBankAccount;

  @FieldInfo(name = "Crop rotation used (Yes/No)")
  @Column(name = "crop_rotation_used")
  private String cropRotationUsed;

  @FieldInfo(name = "Crops used for rotation")
  @Column(name = "rotation_crops")
  private String rotationCrops;

  @FieldInfo(name = "Using any water tracking devices (Yes/No)")
  @Column(name = "water_tracking_devices")
  private String waterTrackingDevices;

  @FieldInfo(name = "Capacity of pump (in HP)")
  @Column(name = "pump_capacity")
  private Integer pumpCapacity;

  @FieldInfo(name = "Maintaining Buffer zone (Yes/No)")
  @Column(name = "buffer_zone")
  private String bufferZone;

  @FieldInfo(name = "Utilization of crop residue (Fuel/cattle feed/biochar/in-situ composting/burning)")
  @Column(name = "crop_residue_utilization")
  private String cropResidueUtilization;

  @FieldInfo(name = "Mode of payment to workers (cash/online)")
  @Column(name = "worker_payment_mode")
  private String workerPaymentMode;

  @FieldInfo(name = "Any wage difference for Men and Women workers (Yes/No)")
  @Column(name = "wage_gender_difference")
  private String wageGenderDifference;

  @FieldInfo(name = "Using any labour register (Yes/No)")
  @Column(name = "labour_register")
  private String labourRegister;

  @FieldInfo(name = "Any arrangement of safety-kit / first-aid for workers")
  @Column(name = "safety_kit_for_workers")
  private String safetyKitForWorkers;

  @FieldInfo(name = "Any provision of shelter & safe drinking water for workers")
  @Column(name = "shelter_and_water_for_workers")
  private String shelterAndWaterForWorkers;

  @FieldInfo(name = "Any provision for lavatory for workers")
  @Column(name = "lavatory_for_workers")
  private String lavatoryForWorkers;

  @FieldInfo(name = "Involve family members (Women) in agricultural operations")
  @Column(name = "women_in_agri_operations")
  private String womenInAgriOperations;

  @FieldInfo(name = "Any community water harvesting structure (Yes/No)")
  @Column(name = "community_water_harvesting")
  private String communityWaterHarvesting;

  @FieldInfo(name = "Use of soil moisture meter (Yes/No)")
  @Column(name = "soil_moisture_meter_used")
  private String soilMoistureMeterUsed;

  @Column(name = "total_hh_members")
  private Integer totalHHMembers;

  @Column(name = "dependency_ratio")
  private Double dependencyRatio;

  @Column(name = "gender_ratio")
  private Double genderRatio;

  @Column(name = "school_attendance_rate")
  private Double schoolAttendanceRate;

  @Column(name = "total_cotton_land")
  private Double totalCottonLand;

  @Column(name = "organic_percent")
  private Double organicPercent;

  @Column(name = "land_used_for_cotton")
  private Double landUsedForCotton;

  @Column(name = "income_per_earner")
  private Double incomePerEarner;

  @Column(name = "oc_income")
  private Double ocIncome;

  @Column(name = "profit_per_acre")
  private Double profitPerAcre;

  @Column(name = "total_certification_cost")
  private Double totalCertificationCost;

  @Column(name = "total_pt_cost")
  private Double totalPTCost;

  @Column(name = "total_yst_cost")
  private Double totalYSTCost;

  @Column(name = "total_bst_cost")
  private Double totalBSTCost;

  @Column(name = "total_pest_mgmt_cost")
  private Double totalPestMgmtCost;

  @Column(name = "total_labour_cost")
  private Double totalLabourCost;

  @Column(name = "machinery_cost_total")
  private Double machineryCostTotal;

  @Column(name = "total_irrigation_cost")
  private Double totalIrrigationCost;

  @Column(name = "irrigation_frequency")
  private Double irrigationFrequency;

  @ManyToOne
  @JoinColumn(name = "excel_file_meta_data_id")
  private ExcelFileMetaData excelFileMetaData;

  public CottonFarmingDataId getId() {
    return new CottonFarmingDataId(farmerId, programId, partnerId, year);
  }
}
