package org.technoserve.udp.entity.dataflow;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.StringListConverter;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Entity to store the history of Excel file metadata
 */
@Entity
@Table(name = "excel_file_meta_data_history")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExcelFileMetaDataHistory implements Serializable {

  @Id
  @Column(name = "history_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long historyId;

  @Column(name = "excel_file_meta_data_id")
  private Long excelFileMetaDataId;

  @Column(name = "program_id")
  private Long programId;

  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "file_name")
  private String fileName;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "file_type")
  private FileType fileType;

  @Column(name = "entity_type", nullable = false)
  private String entityType;

  @Column(name = "path")
  private String path;

  @Column(name = "validation_result_path")
  private String validationResultPath;

  @Column(name = "header_json")
  @Convert(converter = StringListConverter.class)
  private List<String> headerJson;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "uploaded_on")
  private LocalDateTime uploadedOn;

  @Column(name = "uploaded_by")
  private String uploadedBy;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "upload_status")
  private UploadStatus uploadStatus;

  @Column(name = "processed_by")
  private String processedBy;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "processed_on")
  private LocalDateTime processedOn;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "archived_on")
  private LocalDateTime archivedOn;

  @Column(name = "archived_by")
  private String archivedBy;

  /**
   * Create a history record from an ExcelFileMetaData entity
   * 
   * @param metadata The ExcelFileMetaData entity
   * @return A new ExcelFileMetaDataHistory entity
   */
  public static ExcelFileMetaDataHistory fromExcelFileMetaData(ExcelFileMetaData metadata) {
    return ExcelFileMetaDataHistory.builder()
        .excelFileMetaDataId(metadata.getExcelFileMetaDataId())
        .programId(metadata.getProgram() != null ? metadata.getProgram().getProgramId() : null)
        .partnerId(metadata.getPartner() != null ? metadata.getPartner().getPartnerId() : null)
        .fileName(metadata.getFileName())
        .fileType(metadata.getFileType())
        .entityType(metadata.getEntityType())
        .path(metadata.getPath())
        .validationResultPath(metadata.getValidationResultPath())
        .headerJson(metadata.getHeaderJson())
        .uploadedOn(metadata.getUploadedOn())
        .uploadedBy(metadata.getUploadedBy())
        .uploadStatus(metadata.getUploadStatus())
        .processedBy(metadata.getProcessedBy())
        .processedOn(metadata.getProcessedOn())
        .archivedBy(metadata.getArchivedBy())
        .archivedOn(metadata.getArchivedOn())
        .build();
  }
}
