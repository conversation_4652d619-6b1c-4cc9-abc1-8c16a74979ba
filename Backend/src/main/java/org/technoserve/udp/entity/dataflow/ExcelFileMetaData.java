package org.technoserve.udp.entity.dataflow;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.technoserve.udp.entity.common.StringListConverter;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

import java.io.Serializable;
import java.util.List;
import java.time.LocalDateTime;

@Entity
@Table(name = "excel_file_meta_data")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ExcelFileMetaData implements Serializable {

  @Id
  @Column(name = "excel_file_meta_data_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long excelFileMetaDataId;

  @ManyToOne
  @JoinColumn(name = "program_id")
  private Program program;

  @ManyToOne
  @JoinColumn(name = "partner_id")
  private Partner partner;

  @Column(name = "file_name")
  private String fileName;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "file_type")
  private FileType fileType;

  @Column(name = "entity_type", nullable = false)
  private String entityType;

  @Column(name = "path")
  private String path;

  @Column(name = "validation_result_path")
  private String validationResultPath;

  @Column(name = "header_json")
  @Convert(converter = StringListConverter.class)
  private List<String> headerJson;

  @JsonIgnore
  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @CreationTimestamp
  @Column(updatable = false)
  private LocalDateTime uploadedOn;

  @JsonIgnore
  @CreatedBy
  @JoinColumn(name = "uploaded_by")
  @JsonBackReference
  private String uploadedBy;

  @Column(name = "processed_by")
  private String processedBy;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "processed_on")
  private LocalDateTime processedOn;

  @Column(name = "archived_by")
  private String archivedBy;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "archived_on")
  private LocalDateTime archivedOn;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "upload_status")
  private UploadStatus uploadStatus;

}
