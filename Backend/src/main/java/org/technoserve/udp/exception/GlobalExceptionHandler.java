package org.technoserve.udp.exception;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.technoserve.udp.dto.ApiErrorResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@ControllerAdvice
public class GlobalExceptionHandler {


  @ExceptionHandler(ConflictException.class)
  @ResponseStatus(HttpStatus.CONFLICT)
  public ResponseEntity<Object> handleConflict(ConflictException ex) {
    ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.CONFLICT.value(), ex.getMessage(),"");
    return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
  }

  @ExceptionHandler(BadRequestException.class)
  @ResponseStatus(HttpStatus.CONFLICT)
  public ResponseEntity<Object> handleBadRequest(BadRequestException ex) {
   ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage(),"");
    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(FileTypeNotSupportedException.class)
  @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
  public ResponseEntity<Object> handleFileTypeNotSupported(FileTypeNotSupportedException ex) {
    ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.UNPROCESSABLE_ENTITY.value(), ex.getMessage(),"");
    return new ResponseEntity<>(errorResponse, HttpStatus.UNPROCESSABLE_ENTITY);
  }

  @ExceptionHandler(IllegalArgumentException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ResponseEntity<Object> handleIllegalArgumentException(IllegalArgumentException ex) {
    ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage(),"");
    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
  }
  // Handle 404 Not Found
  @ExceptionHandler(ResourceNotFoundException.class)
  @ResponseStatus(HttpStatus.NOT_FOUND)
  public ResponseEntity<ApiErrorResponse> handleNotFoundException(ResourceNotFoundException ex) {
    ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.NOT_FOUND.value(),  ex.getMessage(),"");
    return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
  }

  // Handles validation errors from @Valid in @RequestBody
  @ExceptionHandler(MethodArgumentNotValidException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ResponseEntity<Map<String, Object>> handleValidationExceptions(MethodArgumentNotValidException ex) {
    Map<String, Object> errors = new HashMap<>();
    errors.put("errorCode", HttpStatus.BAD_REQUEST.value());
    errors.put("errorMessage", "Validation failed");

    Map<String, String> fieldErrors = new HashMap<>();
    for (FieldError error : ex.getBindingResult().getFieldErrors()) {
      fieldErrors.put(error.getField(), error.getDefaultMessage());
    }
    errors.put("errorDetails", fieldErrors);
    return ResponseEntity.badRequest().body(errors);
  }

  // Handles validation errors from @Valid in @PathVariable or @RequestParam
  @ExceptionHandler(ConstraintViolationException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ResponseEntity<Map<String, Object>> handleConstraintViolationException(ConstraintViolationException ex) {
    Map<String, Object> response = new HashMap<>();
    response.put("errorCode", HttpStatus.BAD_REQUEST.value());
    response.put("errorMessage", "Validation failed");

    Map<String, String> errors = ex.getConstraintViolations().stream()
        .collect(Collectors.toMap(
            violation -> violation.getPropertyPath().toString().substring(violation.getPropertyPath().toString().lastIndexOf('.') + 1),
            ConstraintViolation::getMessage,
            (existing, replacement) -> existing  // Keep first error if duplicates exist
        ));

    response.put("errorDetails", errors);

    return ResponseEntity.badRequest().body(response);
  }

  @ExceptionHandler(UserNotFoundException.class)
  public void handleUserNotFoundException(UserNotFoundException ex, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws IOException {
    String requestURL = httpServletRequest.getRequestURL().toString();
    httpServletResponse.sendRedirect(requestURL.substring(0,requestURL.indexOf("/udp/")) +"/admin-contact");
  }

 // Handle Internal Server Error (Generic Exception)
  @ExceptionHandler(Exception.class)
  public ResponseEntity<ApiErrorResponse> handleInternalServerError(Exception ex) {
    ApiErrorResponse errorResponse = new ApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred ", ex.getMessage());
    return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  // Handle user already exists
  @ExceptionHandler(UserAlreadyExistsException.class)
  public ResponseEntity<Object> handleUserAlreadyExists(UserAlreadyExistsException ex) {
    return buildResponse(HttpStatus.CONFLICT, ex.getMessage());
  }

  // Handle role not found
  @ExceptionHandler(RoleNotFoundException.class)
  public ResponseEntity<Object> handleRoleNotFound(RoleNotFoundException ex) {
    return buildResponse(HttpStatus.NOT_FOUND, ex.getMessage());
  }

  @ExceptionHandler(DataIntegrityViolationException.class)
  public ResponseEntity<Object> handleDataIntegrityViolation(DataIntegrityViolationException ex) {
    String message = ex.getMostSpecificCause().getMessage();
    // Customize based on constraint name
    if (message != null && message.contains("fk_farmer_centre")) {
      return buildResponse(HttpStatus.CONFLICT, "Cannot delete one or more centres: Each has associated farmers.");
    }else if (message != null && message.contains("fk_cotton_farming_data_farmer")){
      return buildResponse(HttpStatus.CONFLICT, "Cannot delete one or more farmers: Each has associated transactional data.");
    } else if (message != null && message.contains("fk_milk_quality_centre")) {
      return buildResponse(HttpStatus.CONFLICT, "Cannot delete one or more centres: Each has associated transactional data.");
    } else if (message != null && message.contains("fk_staff_centre")) {
      return buildResponse(HttpStatus.CONFLICT, "Cannot delete one or more centres: Each has associated staff.");
    }
    else return buildResponse(HttpStatus.CONFLICT, "A data integrity violation occurred.");


  }

  private ResponseEntity<Object> buildResponse(HttpStatus status, String message) {
    Map<String, Object> body = new HashMap<>();
    body.put("timestamp", LocalDateTime.now());
    body.put("status", status.value());
    body.put("error", status.getReasonPhrase());
    body.put("message", message);

    return new ResponseEntity<>(body, status);
  }
}
