package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.dto.MilkQualityAggregateDto;
import org.technoserve.udp.entity.dataflow.MilkQuality;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface MilkQualityRepository extends JpaRepository<MilkQuality, Long> {

    @Modifying
    @Query("DELETE FROM MilkQuality m WHERE m.excelFileMetaData.excelFileMetaDataId = :excelFileMetaDataId")
    void deleteByExcelFileMetaDataId(@Param("excelFileMetaDataId") Long excelFileMetaDataId);


    /**
     * Find milk quality data by partner ID, program ID, and date range
     * This method should be used when both start and end dates are provided
     *
     * @param partnerId The partner ID
     * @param programId The program ID
     * @param startDate The start date
     * @param endDate The end date
     * @return List of milk quality data
     */
    @Query("SELECT m FROM MilkQuality m WHERE " +
        "(:partnerId IS NULL OR m.partnerId = :partnerId) AND " +
        "(:programId IS NULL OR m.programId = :programId) AND " +
        "m.date BETWEEN :startDate AND :endDate")
    List<MilkQuality> findByPartnerIdAndProgramIdAndDateBetween(
        @Param("partnerId") Long partnerId,
        @Param("programId") Long programId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate);

    /**
     * Find aggregated milk quality data by partner ID, program ID, and date range
     * This method groups the data by centreId, programId, and partnerId and calculates aggregated values
     *
     * @param partnerId The partner ID
     * @param programId The program ID
     * @param startDate The start date
     * @param endDate The end date
     * @return List of aggregated milk quality data
     */
    @Query(value = "SELECT m.centre_id as centreId, " +
        "m.program_id as programId, " +
        "m.partner_id as partnerId, " +
        "ROUND(SUM(COALESCE(m.total_milk_volume, 0))::numeric, 2) as totalMilkVolume, " +
        "ROUND(AVG(COALESCE(m.fat, 0))::numeric, 2) as avgFat, " +
        "ROUND(AVG(COALESCE(m.snf, 0))::numeric, 2) as avgSnf, " +
        "ROUND(SUM(COALESCE(m.segregated_milk_volume, 0))::numeric, 2) as segregatedMilkVolume, " +
        "ROUND(SUM(COALESCE(m.ab_positive_milk_volume, 0))::numeric, 2) as abPositiveMilkVolume, " +
        "ROUND(SUM(COALESCE(m.afm1_positive_milk_volume, 0))::numeric, 2) as afm1PositiveMilkVolume, " +
        "ROUND(SUM(COALESCE(m.high_sodium_milk_volume, 0))::numeric, 2) as highSodiumMilkVolume, " +
        "ROUND(SUM(COALESCE(m.overall_positive_milk_volume, 0))::numeric, 2) as overallPositiveMilkVolume, " +
        "ROUND(SUM(COALESCE(m.overall_negative_milk_volume, 0))::numeric, 2) as overallNegativeMilkVolume, " +
        "ROUND(SUM(COALESCE(m.qbi_volume_milk_volume, 0))::numeric, 2) as qbiVolumeMilkVolume, " +
        "ROUND(SUM(COALESCE(m.beta_pos_volume, 0))::numeric, 2) as betaPosVolume, " +
        "ROUND(SUM(COALESCE(m.sulfa_pos_volume, 0))::numeric, 2) as sulfaPosVolume, " +
        "ROUND(SUM(COALESCE(m.tetra_pos_volume, 0))::numeric, 2) as tetraPosVolume, " +
        "ROUND(SUM(COALESCE(m.cap_pos_volume, 0))::numeric, 2) as capPosVolume, " +
        "COUNT(m.milk_quality_id) as recordCount " +
        "FROM milk_quality m " +
        "WHERE (:partnerId IS NULL OR m.partner_id = :partnerId) " +
        "AND (:programId IS NULL OR m.program_id = :programId) " +
        "AND m.date BETWEEN :startDate AND :endDate " +
        "GROUP BY m.centre_id, m.program_id, m.partner_id", nativeQuery = true)
    List<MilkQualityAggregateDto> findAggregatedByPartnerIdAndProgramIdAndDateBetween(
        @Param("partnerId") Long partnerId,
        @Param("programId") Long programId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate);


  boolean existsByCentreIdAndDateAndProgramIdAndPartnerId(String centreId, LocalDate date, Long programId, Long partnerId);
}
