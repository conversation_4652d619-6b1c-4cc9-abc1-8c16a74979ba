package org.technoserve.udp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.technoserve.udp.entity.dataflow.UploadStatus;

import java.time.LocalDateTime;

/**
 * DTO for Excel file metadata response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelFileMetaDataResponse {
    private Long id;
    private String fileName;
    private String path;
    private Long programId;
    private String programName;
    private Long partnerId;
    private String partnerName;
    private String validationResultPath;
    private String entityType;
    private UploadStatus uploadStatus;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadedOn;

    private String uploadedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processedOn;

    private String processedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime archivedOn;

    private String archivedBy;


}
