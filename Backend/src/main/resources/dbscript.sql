

CREATE TYPE public.status AS ENUM
    ('APPROVED', 'CREATED' ,'CHECKED', 'DELETED', 'DRAFT', 'REJECTED', 'SUBMITTED');

ALTER TYPE public.status
    OWNER TO postgres;

CREATE TYPE public.budgetfrequency AS ENUM
    ('ANNUALLY', 'BI_MONTHLY', 'DAILY', 'FORTNIGHTLY', 'HALF_YEARLY', 'MONTHLY', 'QUARTERLY', 'SEASONALLY', 'TEN_DAYS', 'WEEKLY');

ALTER TYPE public.budgetfrequency
    OWNER TO postgres;

CREATE TYPE public.dimensiontype AS ENUM
    ('ECONOMIC', 'ENVIRONMENTAL', 'SOCIAL');

ALTER TYPE public.dimensiontype
    OWNER TO postgres;

CREATE TYPE public.microactivityfrequency AS ENUM
    ('ANNUALLY', 'BI_MONTHLY', 'DAILY', 'FORTNIGHTLY', 'HALF_YEARLY', 'MONTHLY', 'QUARTERLY', 'SEASONALLY', 'TEN_DAYS', 'WEEKLY');

ALTER TYPE public.microactivityfrequency
    OWNER TO postgres;

CREATE TYPE public.microactivitytype AS ENUM
    ('FINANCIAL', 'NON_FINANCIAL');

ALTER TYPE public.microactivitytype
    OWNER TO postgres;

CREATE TYPE public.programphase AS ENUM
    ('ACTIVE', 'INACTIVE');

ALTER TYPE public.programphase
    OWNER TO postgres;

CREATE TYPE public.upload_status AS ENUM
    ('UPLOADED', 'PROCESSED', 'FAILED', 'DELETED');

ALTER TYPE public.upload_status
    OWNER TO postgres;

CREATE TYPE public.entity_type AS ENUM
    ('FARMER', 'CENTRE');

ALTER TYPE public.entity_type
    OWNER TO postgres;

CREATE TABLE public.users (
    email character varying(255) NOT NULL,
    name character varying(255),
	status character varying(255),
	created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT users_pkey PRIMARY KEY (email),
	CONSTRAINT fk_users_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_users_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.users OWNER TO postgres;


CREATE TABLE public.roles (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
	created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT roles_pkey PRIMARY KEY (id),
	CONSTRAINT fk_roles_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_roles_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.roles OWNER TO postgres;

ALTER TABLE public.roles ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.user_roles (
    user_email character varying(255) NOT NULL,
    role_id bigint NOT NULL,
	CONSTRAINT user_roles_pkey PRIMARY KEY (user_email, role_id),
	CONSTRAINT fk_user_roles_users FOREIGN KEY (user_email) REFERENCES public.users(email),
	CONSTRAINT fk_user_roles_roles FOREIGN KEY (role_id) REFERENCES public.roles(id)
);


ALTER TABLE public.user_roles OWNER TO postgres;



CREATE TABLE IF NOT EXISTS public.screen
(
    id bigint NOT NULL,
    name character varying(255),
    role_id bigint NOT NULL,
    CONSTRAINT screen_pkey PRIMARY KEY (id),
    CONSTRAINT unique_screen_role UNIQUE (name, role_id)
);


ALTER TABLE public.screen OWNER TO postgres;

ALTER TABLE public.screen ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.screen_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
CREATE TABLE IF NOT EXISTS public.permissions
(
    id bigint NOT NULL,
    created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
    label_name character varying(255),
    name character varying(255),
    created_by character varying(255),
    updated_by character varying(255),
    master_screen_id bigint,
    CONSTRAINT permissions_pkey PRIMARY KEY (id),
    CONSTRAINT permissions_unique_name UNIQUE (name, master_screen_id)
);


ALTER TABLE public.permissions OWNER TO postgres;

ALTER TABLE public.permissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.permissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE IF NOT EXISTS public.property
(
    id bigint NOT NULL,
    label_name character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
    master_screen_id bigint,
    CONSTRAINT property_pkey PRIMARY KEY (id),
    CONSTRAINT unique_property_name_master_screen UNIQUE (name, master_screen_id),
	CONSTRAINT fk_property_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_property_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.property OWNER TO postgres;

ALTER TABLE public.property ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.property_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE IF NOT EXISTS public.screen_permissions
(
    screen_id bigint NOT NULL,
    permission_id bigint NOT NULL,
	CONSTRAINT screen_permissions_pkey PRIMARY KEY (screen_id, permission_id),
	CONSTRAINT fk_screen_permissions_users FOREIGN KEY (screen_id) REFERENCES public.screen(id),
	CONSTRAINT fk_screen_permissions_permissions FOREIGN KEY (permission_id) REFERENCES public.permissions(id)
);

CREATE TABLE IF NOT EXISTS public.screen_properties
(
    screen_id bigint NOT NULL,
    property_id bigint NOT NULL,
	CONSTRAINT sscreen_properties_pkey PRIMARY KEY (screen_id, property_id),
	CONSTRAINT fk_screen_properties_users FOREIGN KEY (screen_id) REFERENCES public.screen(id),
	CONSTRAINT fk_screen_properties_property FOREIGN KEY (property_id) REFERENCES public.property(id)
);

CREATE TABLE value_chain_type (
    type character varying,
    CONSTRAINT pk_value_chain_type_name PRIMARY KEY (type)
);

INSERT INTO value_chain_type VALUES ('Dairy'), ('Cotton');

CREATE TABLE public.value_chain (
    value_chain_id bigint NOT NULL,
    logourl character varying(255),
    type character varying NOT NULL,
    name character varying(255) NOT NULL,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	status public.status,
	CONSTRAINT value_chain_pkey PRIMARY KEY (value_chain_id),
	CONSTRAINT fk_value_chain_type FOREIGN KEY (type) REFERENCES public.value_chain_type(type),
	CONSTRAINT fk_value_chain_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_value_chain_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);

ALTER TABLE public.value_chain OWNER TO postgres;

ALTER TABLE public.value_chain ALTER COLUMN value_chain_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.value_chain_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);




CREATE TABLE public.role_value_chain (
    role_id bigint NOT NULL,
    value_chain_id bigint NOT NULL,
	CONSTRAINT role_value_chain_pkey PRIMARY KEY (role_id, value_chain_id),
	CONSTRAINT fk_role_value_chain_role FOREIGN KEY (role_id) REFERENCES public.roles(id),
	CONSTRAINT fk_role_value_chain_value_chain_id FOREIGN KEY (value_chain_id) REFERENCES public.value_chain(value_chain_id)
);


ALTER TABLE public.role_value_chain OWNER TO postgres;






CREATE TABLE public.budget (
    budget_id bigint NOT NULL,
    budget_frequency public.budgetfrequency,
    no_of_units numeric(38,2),
    total_cost numeric(38,2),
    unit character varying(255),
    unit_cost numeric(38,2),
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT budget_pkey PRIMARY KEY (budget_id),
	CONSTRAINT fk_budget_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_budget_users_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);

ALTER TABLE public.budget OWNER TO postgres;

ALTER TABLE public.budget ALTER COLUMN budget_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.budget_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE public.budget_year_wise (
    budget_year_wise_id bigint NOT NULL,
    farmer_contribution numeric(38,2),
    ip numeric(38,2),
    project_sponsor_contribution numeric(38,2),
    total_amount numeric(38,2),
    created_by character varying(255),
    updated_by character varying(255),
    budget_id bigint,
    status public.status,
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT budget_year_wise_pkey PRIMARY KEY (budget_year_wise_id),
	CONSTRAINT fk_budget_year_wise_budget FOREIGN KEY (budget_id) REFERENCES public.budget(budget_id),
	CONSTRAINT fk_budget_year_wise_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_budget_year_wise_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);

ALTER TABLE public.budget_year_wise OWNER TO postgres;

ALTER TABLE public.budget_year_wise ALTER COLUMN budget_year_wise_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.budget_year_wise_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE public.change_log (
    id bigint NOT NULL,
    action character varying(255),
    changes text,
    entity_id character varying(255),
    entity_name character varying(255),
    modified_at timestamp(6) without time zone,
    modified_by character varying(255),
	CONSTRAINT change_log_pkey PRIMARY KEY (id)
);


ALTER TABLE public.change_log OWNER TO postgres;

ALTER TABLE public.change_log ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.change_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE public.program_log_frame (
    program_log_frame_id bigint NOT NULL,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
    CONSTRAINT program_log_frame_pkey PRIMARY KEY (program_log_frame_id),
	CONSTRAINT fk_program_log_frame_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_program_log_frame_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.program_log_frame OWNER TO postgres;


ALTER TABLE public.program_log_frame ALTER COLUMN program_log_frame_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.program_log_frame_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.dimension (
    dimension_id bigint NOT NULL,
    type public.dimensiontype,
    status public.status,
    created_by character varying(255),
    updated_by character varying(255),
    program_log_frame_id bigint,
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT dimension_pkey PRIMARY KEY (dimension_id),
	CONSTRAINT fk_dimension_program_log_frame FOREIGN KEY (program_log_frame_id) REFERENCES public.program_log_frame(program_log_frame_id),
	CONSTRAINT fk_dimension_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_dimension_users FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.dimension OWNER TO postgres;


ALTER TABLE public.dimension ALTER COLUMN dimension_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.dimension_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE public.program_fact_sheet (
    program_fact_sheet_id bigint NOT NULL,
    end_year date NOT NULL,
    program_goal character varying(255),
    program_phase public.programphase,
    start_year date NOT NULL,
    total_farmers integer,
    total_female_farmers integer,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT program_fact_sheet_pkey PRIMARY KEY (program_fact_sheet_id),
	CONSTRAINT fk_program_fact_sheet_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_program_fact_sheet_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.program_fact_sheet OWNER TO postgres;


ALTER TABLE public.program_fact_sheet ALTER COLUMN program_fact_sheet_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.program_fact_sheet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.master_country (
    country_name character varying(255) NOT NULL,
    CONSTRAINT master_country_pkey PRIMARY KEY (country_name)
);


ALTER TABLE public.master_country OWNER TO postgres;


CREATE TABLE public.master_state (

    master_state_id bigint NOT NULL,
    state_name character varying(255) NOT NULL,
	master_country_id character varying(255),
    CONSTRAINT master_state_pkey PRIMARY KEY (master_state_id),
	CONSTRAINT fk_master_state_master_country FOREIGN KEY (master_country_id) REFERENCES public.master_country(country_name)

);


ALTER TABLE public.master_state OWNER TO postgres;


ALTER TABLE public.master_state ALTER COLUMN master_state_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.master_state_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);




CREATE TABLE public.master_district (
    master_district_id bigint NOT NULL,
    district_name character varying(255),
    master_state_id bigint,
	CONSTRAINT master_district_pkey PRIMARY KEY (master_district_id),
	CONSTRAINT fk_master_district_master_state FOREIGN KEY (master_state_id) REFERENCES public.master_state(master_state_id)
);


ALTER TABLE public.master_district OWNER TO postgres;


ALTER TABLE public.master_district ALTER COLUMN master_district_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.master_district_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);





CREATE TABLE public.district_data (
    district_data_id bigint NOT NULL,
    number_of_village integer,
	master_district_id bigint,
	program_fact_sheet_id bigint,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT district_data_pkey PRIMARY KEY (district_data_id),
	CONSTRAINT fk_district_data_state_data FOREIGN KEY (master_district_id) REFERENCES public.master_district(master_district_id),
    CONSTRAINT fk_district_data_program_fact_sheet FOREIGN KEY (program_fact_sheet_id) REFERENCES public.program_fact_sheet(program_fact_sheet_id),
	CONSTRAINT fk_district_data_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
    CONSTRAINT fk_district_data_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);

ALTER TABLE public.district_data OWNER TO postgres;


ALTER TABLE public.district_data ALTER COLUMN district_data_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.district_data_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);




CREATE TABLE public.sponsor (
    sponsor_id bigint NOT NULL,
    logourl character varying(255),
    name character varying(255) NOT NULL,
    status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT sponsor_pkey PRIMARY KEY (sponsor_id),
	CONSTRAINT fk_sponsor_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_sponsor_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);


ALTER TABLE public.sponsor OWNER TO postgres;


ALTER TABLE public.sponsor ALTER COLUMN sponsor_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.sponsor_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE public.endpoints (
    id bigint NOT NULL,
    http_method character varying(255) NOT NULL,
    pattern character varying(255) NOT NULL,
	created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT endpoints_pkey PRIMARY KEY (id),
	CONSTRAINT fk_endpoints_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_endpoints_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.endpoints OWNER TO postgres;


ALTER TABLE public.endpoints ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.endpoints_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE IF NOT EXISTS public.master_screen
(
    id bigint NOT NULL,
    name character varying(255),
	created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
    CONSTRAINT master_screen_pkey PRIMARY KEY (id),
    CONSTRAINT master_screen_unique_name UNIQUE (name)
);

ALTER TABLE public.master_screen OWNER TO postgres;

ALTER TABLE public.master_screen ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.master_screen_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);






CREATE TABLE IF NOT EXISTS public.permission_endpoints
(
    permission_id bigint NOT NULL,
    endpoint_id bigint NOT NULL,
	CONSTRAINT permission_endpoints_pkey PRIMARY KEY (permission_id, endpoint_id),
	CONSTRAINT fk_permission_endpoints_roles FOREIGN KEY (permission_id) REFERENCES public.permissions(id),
	CONSTRAINT fk_permission_endpoints_endpoints FOREIGN KEY (endpoint_id) REFERENCES public.endpoints(id)
);




CREATE TABLE public.program (
    program_id bigint NOT NULL,
    status public.status,
    approved_at timestamp(6) without time zone,
    checked_at timestamp(6) without time zone,
    comment character varying(255),
    logourl character varying(255),
    name character varying(255) NOT NULL,
	program_fact_sheet_id bigint,
    program_log_frame_id bigint,
    value_chain_id bigint,
    created_by character varying(255),
    updated_by character varying(255),
    approved_by character varying(255),
    checked_by character varying(255),
    created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT program_pkey PRIMARY KEY (program_id),
	CONSTRAINT fk_program_program_fact_sheet FOREIGN KEY (program_fact_sheet_id) REFERENCES public.program_fact_sheet(program_fact_sheet_id),
	CONSTRAINT fk_program_program_log_frame FOREIGN KEY (program_log_frame_id) REFERENCES public.program_log_frame(program_log_frame_id),
	CONSTRAINT fk_program_value_chain FOREIGN KEY (value_chain_id) REFERENCES public.value_chain(value_chain_id),
	CONSTRAINT fk_program_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_program_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email),
	CONSTRAINT fk_program_checked_by FOREIGN KEY (checked_by) REFERENCES public.users(email),
	CONSTRAINT fk_program_approved_by FOREIGN KEY (approved_by) REFERENCES public.users(email)
);


ALTER TABLE public.program OWNER TO postgres;


ALTER TABLE public.program ALTER COLUMN program_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.program_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE public.fund_code (
    fund_code_id bigint NOT NULL,
    budget_allocation numeric(38,2),
    contribution integer,
    fund_code character varying(255),
    implementation_budget numeric(38,2),
    pmu_budget numeric(38,2),
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
    program_id bigint,
    sponsor_id bigint,
    status public.status,
	CONSTRAINT fund_code_pkey PRIMARY KEY (fund_code_id),
	CONSTRAINT fk_fund_code_program FOREIGN KEY (program_id) REFERENCES public.program(program_id),
	CONSTRAINT fk_fund_code_sponsor FOREIGN KEY (sponsor_id) REFERENCES public.sponsor(sponsor_id),
	CONSTRAINT fk_fund_code_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_fund_code_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);


ALTER TABLE public.fund_code OWNER TO postgres;


ALTER TABLE public.fund_code ALTER COLUMN fund_code_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.fund_code_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.objective (
    objective_id bigint NOT NULL,
    end_of_program_target numeric(38,2),
    kpi_name character varying(255),
    logourl character varying(255),
    outcome_indicator character varying(255),
    unit_of_measurement character varying(255),
	dimension_id bigint,
	status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT objective_pkey PRIMARY KEY (objective_id),
	CONSTRAINT fk_objective_dimension FOREIGN KEY (dimension_id) REFERENCES public.dimension(dimension_id),
	CONSTRAINT fk_objective_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
    CONSTRAINT fk_objective_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);


ALTER TABLE public.objective OWNER TO postgres;


ALTER TABLE public.objective ALTER COLUMN objective_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.objective_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE public.output_indicator (
    output_indicator_id bigint NOT NULL,
    output_indicator_name character varying(255),
    unit_of_measurement character varying(255),
    yy_target numeric(38,2),
	objective_id bigint,
	status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT output_indicator_pkey PRIMARY KEY (output_indicator_id),
	CONSTRAINT fk_output_indicator_objective FOREIGN KEY (objective_id) REFERENCES public.objective(objective_id),
	CONSTRAINT fk_output_indicator_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_output_indicator_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.output_indicator OWNER TO postgres;


ALTER TABLE public.output_indicator ALTER COLUMN output_indicator_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.output_indicator_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.key_activity (
    key_activity_id bigint NOT NULL,
    key_activity_name character varying(255),
	output_indicator_id bigint,
	status public.status,
	created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT key_activity_pkey PRIMARY KEY (key_activity_id),
	CONSTRAINT fk_key_activity_output_indicator FOREIGN KEY (output_indicator_id) REFERENCES public.output_indicator(output_indicator_id),
	CONSTRAINT fk_key_activity_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_key_activity_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);


ALTER TABLE public.key_activity OWNER TO postgres;


ALTER TABLE public.key_activity ALTER COLUMN key_activity_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.key_activity_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);






CREATE TABLE public.sub_activity (
    sub_activity_id bigint NOT NULL,
    sub_activity_name character varying(255),
	key_activity_id bigint,
	status public.status,
    created_by character varying(255),
    updated_by character varying(255),
    created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT sub_activity_pkey PRIMARY KEY (sub_activity_id),
	CONSTRAINT fk_sub_activity_key_activity FOREIGN KEY (key_activity_id) REFERENCES public.key_activity(key_activity_id),
	CONSTRAINT fk_sub_activity_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_sub_activity_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.sub_activity OWNER TO postgres;


ALTER TABLE public.sub_activity ALTER COLUMN sub_activity_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.sub_activity_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.micro_activity (
    micro_activity_id bigint NOT NULL,
    accountable_party character varying(255),
    consulted_party character varying(255),
    micro_activity_frequency public.microactivityfrequency,
    informed_party character varying(255),
    micro_activity_name character varying(255),
    micro_activity_type public.microactivitytype,
    responsible_party character varying(255),
    tools character varying(255),
	budget_id bigint,
    sub_activity_id bigint,
    status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT micro_activity_pkey PRIMARY KEY (micro_activity_id),
	CONSTRAINT fk_micro_activity_budget FOREIGN KEY (budget_id) REFERENCES public.budget(budget_id),
	CONSTRAINT fk_micro_activity_sub_activity FOREIGN KEY (sub_activity_id) REFERENCES public.sub_activity(sub_activity_id),
	CONSTRAINT fk_micro_activity_users FOREIGN KEY (created_by) REFERENCES public.users(email),
    CONSTRAINT fk_micro_activity_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)

);


ALTER TABLE public.micro_activity OWNER TO postgres;


ALTER TABLE public.micro_activity ALTER COLUMN micro_activity_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.micro_activity_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);









CREATE TABLE public.partner (
    partner_id bigint NOT NULL,
    logourl character varying(255),
    name character varying(255),
    status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT partner_pkey PRIMARY KEY (partner_id),
	CONSTRAINT fk_partner_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_partner_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.partner OWNER TO postgres;


ALTER TABLE public.partner ALTER COLUMN partner_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.partner_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);






CREATE TABLE public.program_fact_sheet_partners (
    program_fact_sheet_id bigint NOT NULL,
    partner_id bigint NOT NULL,
	CONSTRAINT fk_program_fact_sheet_partners_program_fact_sheet FOREIGN KEY (program_fact_sheet_id) REFERENCES public.program_fact_sheet(program_fact_sheet_id),
	CONSTRAINT fk_program_fact_sheet_partners_partner FOREIGN KEY (partner_id) REFERENCES public.partner(partner_id)

);


ALTER TABLE public.program_fact_sheet_partners OWNER TO postgres;






















CREATE TABLE public.teams (
    team_id bigint NOT NULL,
    name character varying(255),
    rank integer,
    title character varying(255),
	program_id bigint,
	status public.status,
    created_by character varying(255),
    updated_by character varying(255),
	created_on timestamp(6) without time zone,
    updated_on timestamp(6) without time zone,
	CONSTRAINT teams_pkey PRIMARY KEY (team_id),
	CONSTRAINT fk_teams_program FOREIGN KEY (program_id) REFERENCES public.program(program_id),
	CONSTRAINT fk_teams_created_by FOREIGN KEY (created_by) REFERENCES public.users(email),
	CONSTRAINT fk_teams_updated_by FOREIGN KEY (updated_by) REFERENCES public.users(email)
);


ALTER TABLE public.teams OWNER TO postgres;



ALTER TABLE public.teams ALTER COLUMN team_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.teams_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE public.excel_file_meta_data (
    excel_file_meta_data_id bigint NOT NULL,
    program_id bigint NOT NULL,
    partner_id bigint NOT NULL,
    entity_type public.entity_type,
    file_name character varying(255),
    path character varying,
    validation_result_path character varying,
    header_json character varying,
    uploaded_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by character varying(255),
    processed_on TIMESTAMP,
    processed_by character varying(255),
    upload_status public.upload_status,
    CONSTRAINT excel_file_meta_data_pkey PRIMARY KEY (excel_file_meta_data_id),
    CONSTRAINT fk_program FOREIGN KEY (program_id) REFERENCES program(program_id),
    CONSTRAINT fk_partner FOREIGN KEY (partner_id) REFERENCES partner(partner_id),
    CONSTRAINT fk_excel_file_meta_data_users FOREIGN KEY (uploaded_by) REFERENCES public.users(email)
);

ALTER TABLE public.excel_file_meta_data OWNER TO postgres;



ALTER TABLE public.excel_file_meta_data ALTER COLUMN excel_file_meta_data_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.excel_file_meta_data_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


CREATE TABLE column_mapping_template (
    column_mapping_template_id bigint NOT NULL,
    program_id bigint NOT NULL,
    partner_id bigint NOT NULL,
    entity_type public.entity_type,
    excel_column character varying(255),
    entity_field character varying(255),
    CONSTRAINT column_mapping_template_pkey PRIMARY KEY (column_mapping_template_id),
    CONSTRAINT fk_program FOREIGN KEY (program_id) REFERENCES program(program_id),
    CONSTRAINT fk_partner FOREIGN KEY (partner_id) REFERENCES partner(partner_id)
);

ALTER TABLE public.column_mapping_template OWNER TO postgres;

ALTER TABLE public.column_mapping_template ALTER COLUMN column_mapping_template_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.column_mapping_template_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TYPE public.file_type AS ENUM
    ('MASTER', 'TRANSACTIONAL');

alter table excel_file_meta_data add file_type public.file_type;

alter table column_mapping_template add file_type public.file_type;



CREATE TABLE centre_type (
    type character varying PRIMARY KEY
);

CREATE TABLE IF NOT EXISTS public.centre
(
    centre_id character varying NOT NULL,
    centre_name character varying,
    centre_type character varying,
    route_no character varying,
    route_name character varying,
    facilitator_name character varying,
    facilitator_id character varying,
    facilitator_country_code character varying DEFAULT '91',
    facilitator_mobile_number character varying,
    state character varying,
    district character varying,
    taluk character varying,
    village character varying,
    license_certification_type character varying,
    license_certification_status character varying,
    license_certification_no character varying,
    latitude double precision,
    longitude double precision,
    installed_capacity integer,
    ics_type character varying,
    program_id bigint,
    partner_id bigint,
    excel_file_meta_data_id bigint,
    CONSTRAINT centre_pkey PRIMARY KEY (centre_id, program_id, partner_id),
    CONSTRAINT fk_centre_type FOREIGN KEY (centre_type) REFERENCES public.centre_type (type),
    CONSTRAINT fk_excel_file FOREIGN KEY (excel_file_meta_data_id) REFERENCES public.excel_file_meta_data (excel_file_meta_data_id),
    CONSTRAINT fk_partner FOREIGN KEY (partner_id) REFERENCES public.partner (partner_id),
    CONSTRAINT fk_program FOREIGN KEY (program_id) REFERENCES public.program (program_id)
);

ALTER TABLE public.centre
    OWNER to postgres;


CREATE TABLE IF NOT EXISTS public.farmer
(
    farmer_id character varying NOT NULL,
    farmer_name character varying,
    age integer,
    gender character varying,
    state character varying,
    district character varying,
    village character varying,
    country_code character varying DEFAULT '91'::character varying,
    mobile_number character varying,
    marital_status character varying,
    spouse_name character varying,
    caste character varying,
    value_chain character varying,
    program_id bigint NOT NULL,
    partner_id bigint NOT NULL,
    centre_id character varying,
    house_hold_size integer,
    land_size_under_cultivation double precision,
    land_measure_type character varying,
    organic_status character varying,
    herd_size integer,
    any_other_income_generating_activity character varying,
    household_annual_income numeric(38,2),
    agricultural_annual_income numeric(38,2),
    dairy_annual_income numeric(38,2),
    other_annual_income numeric(38,2),
    crops_grown character varying,
    cattle_breed_types character varying,
    loan_amount numeric(38,2),
    agricultural_loan numeric(38,2),
    dairy_loan numeric(38,2),
    lat_long character varying,
    excel_file_meta_data_id bigint,
    CONSTRAINT farmer_pkey PRIMARY KEY (farmer_id, program_id, partner_id),
    CONSTRAINT fk_farmer_excel_file_meta_data FOREIGN KEY (excel_file_meta_data_id)
        REFERENCES public.excel_file_meta_data (excel_file_meta_data_id),
    CONSTRAINT fk_farmer_partner FOREIGN KEY (partner_id)
        REFERENCES public.partner (partner_id),
    CONSTRAINT fk_farmer_program FOREIGN KEY (program_id)
        REFERENCES public.program (program_id),
    CONSTRAINT fk_farmer_centre FOREIGN KEY (centre_id, program_id, partner_id)
        REFERENCES public.centre (centre_id, program_id, partner_id)
);

ALTER TABLE public.farmer
    OWNER to postgres;


CREATE TABLE designation (
    name character varying PRIMARY KEY
);

CREATE TABLE IF NOT EXISTS public.staff
(
    staff_id character varying NOT NULL,
    name character varying,
    designation character varying,
    gender character varying,
    country_code character varying DEFAULT '91',
    mobile_number character varying,
    centre_id character varying,
    district character varying,
    state character varying,
    program_id bigint NOT NULL,
    partner_id bigint NOT NULL,
    excel_file_meta_data_id bigint,
    CONSTRAINT staff_pkey PRIMARY KEY (staff_id, program_id, partner_id),
    CONSTRAINT fk_staff_designation FOREIGN KEY (designation)
        REFERENCES public.designation (name),
    CONSTRAINT fk_staff_excel FOREIGN KEY (excel_file_meta_data_id)
        REFERENCES public.excel_file_meta_data (excel_file_meta_data_id),
    CONSTRAINT fk_staff_partner FOREIGN KEY (partner_id)
        REFERENCES public.partner (partner_id),
    CONSTRAINT fk_staff_program FOREIGN KEY (program_id)
        REFERENCES public.program (program_id),
	    CONSTRAINT fk_staff_centre FOREIGN KEY (centre_id, program_id, partner_id)
        REFERENCES public.centre (centre_id, program_id, partner_id)
);

ALTER TABLE public.staff
    OWNER to postgres;

INSERT INTO designation (name) VALUES
('DEO'),
('MPO'),
('QI'),
('FO'),
('SS'),
('TM'),
('ICS-FA'),
('CRP');


ALTER TYPE public.entity_type ADD VALUE 'STAFF';


CREATE TABLE public.excel_file_meta_data_history (
    history_id BIGINT NOT NULL,
    excel_file_meta_data_id BIGINT NOT NULL,
    program_id BIGINT NOT NULL,
    partner_id BIGINT NOT NULL,
    file_name VARCHAR(255),
    file_type public.file_type,
    entity_type public.entity_type,
    path VARCHAR,
    validation_result_path VARCHAR,
    header_json VARCHAR,
    uploaded_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by VARCHAR(255),
    upload_status public.upload_status,
    processed_by VARCHAR(255),
    processed_on TIMESTAMP,
    archived_on TIMESTAMP,
    archived_by VARCHAR(255),

    CONSTRAINT excel_file_meta_data_history_pkey PRIMARY KEY (history_id),
    CONSTRAINT fk_history_program FOREIGN KEY (program_id) REFERENCES public.program(program_id),
    CONSTRAINT fk_history_partner FOREIGN KEY (partner_id) REFERENCES public.partner(partner_id),
    CONSTRAINT fk_history_uploaded_by FOREIGN KEY (uploaded_by) REFERENCES public.users(email)
);


ALTER TABLE public.excel_file_meta_data_history OWNER TO postgres;

ALTER TABLE public.excel_file_meta_data_history ALTER COLUMN history_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.excel_file_meta_data_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


-- Insert country
INSERT INTO public.master_country (country_name)
VALUES ('India');

-- Insert all states
INSERT INTO public.master_state (state_name, master_country_id) VALUES
('Andhra Pradesh', 'India'),
('Arunachal Pradesh', 'India'),
('Assam', 'India'),
('Bihar', 'India'),
('Chhattisgarh', 'India'),
('Goa', 'India'),
('Gujarat', 'India'),
('Haryana', 'India'),
('Himachal Pradesh', 'India'),
('Jharkhand', 'India'),
('Karnataka', 'India'),
('Kerala', 'India'),
('Madhya Pradesh', 'India'),
('Maharashtra', 'India'),
('Manipur', 'India'),
('Meghalaya', 'India'),
('Mizoram', 'India'),
('Nagaland', 'India'),
('Odisha', 'India'),
('Punjab', 'India'),
('Rajasthan', 'India'),
('Sikkim', 'India'),
('Tamil Nadu', 'India'),
('Telangana', 'India'),
('Tripura', 'India'),
('Uttar Pradesh', 'India'),
('Uttarakhand', 'India'),
('West Bengal', 'India');

-- Insert Union Territories
INSERT INTO public.master_state (state_name, master_country_id) VALUES
('Andaman and Nicobar Islands', 'India'),
('Chandigarh', 'India'),
('Dadra and Nagar Haveli and Daman and Diu', 'India'),
('Delhi', 'India'),
('Jammu and Kashmir', 'India'),
('Ladakh', 'India'),
('Lakshadweep', 'India'),
('Puducherry', 'India');

-- Insert districts for all states

-- 1. Andhra Pradesh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Anantapur', 'Chittoor', 'East Godavari', 'Guntur', 'Krishna', 'Kurnool',
'Prakasam', 'Srikakulam', 'Visakhapatnam', 'Vizianagaram', 'West Godavari',
'YSR Kadapa', 'Nellore', 'Parvathipuram Manyam', 'Alluri Sitharama Raju',
'Anakapalli', 'Kakinada', 'Konaseema', 'Eluru', 'NTR', 'Bapatla', 'Palnadu', 'Tirupati',
'Sri Sathya Sai'
]), master_state_id FROM public.master_state WHERE state_name = 'Andhra Pradesh';

-- 2. Arunachal Pradesh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Tawang', 'West Kameng', 'East Kameng', 'Papum Pare', 'Kurung Kumey', 'Kra Daadi',
'Lower Subansiri', 'Upper Subansiri', 'West Siang', 'East Siang', 'Upper Siang',
'Siang', 'Lower Siang', 'Lepa Rada', 'Lower Dibang Valley', 'Dibang Valley',
'Anjaw', 'Lohit', 'Namsai', 'Changlang', 'Tirap', 'Longding', 'Kamle', 'Pakke Kessang',
'Shi Yomi'
]), master_state_id FROM public.master_state WHERE state_name = 'Arunachal Pradesh';

-- 3. Assam districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Baksa', 'Barpeta', 'Biswanath', 'Bongaigaon', 'Cachar', 'Charaideo', 'Chirang',
'Darrang', 'Dhemaji', 'Dhubri', 'Dibrugarh', 'Dima Hasao', 'Goalpara', 'Golaghat',
'Hailakandi', 'Hojai', 'Jorhat', 'Kamrup', 'Kamrup Metropolitan', 'Karbi Anglong',
'Karimganj', 'Kokrajhar', 'Lakhimpur', 'Majuli', 'Morigaon', 'Nagaon', 'Nalbari',
'Sivasagar', 'Sonitpur', 'South Salmara-Mankachar', 'Tinsukia', 'Udalguri', 'West Karbi Anglong',
'Bajali', 'Tamulpur'
]), master_state_id FROM public.master_state WHERE state_name = 'Assam';

-- 4. Bihar districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Araria', 'Arwal', 'Aurangabad', 'Banka', 'Begusarai', 'Bhagalpur', 'Bhojpur',
'Buxar', 'Darbhanga', 'East Champaran', 'Gaya', 'Gopalganj', 'Jamui', 'Jehanabad',
'Kaimur', 'Katihar', 'Khagaria', 'Kishanganj', 'Lakhisarai', 'Madhepura', 'Madhubani',
'Munger', 'Muzaffarpur', 'Nalanda', 'Nawada', 'Patna', 'Purnia', 'Rohtas', 'Saharsa',
'Samastipur', 'Saran', 'Sheikhpura', 'Sheohar', 'Sitamarhi', 'Siwan', 'Supaul',
'Vaishali', 'West Champaran'
]), master_state_id FROM public.master_state WHERE state_name = 'Bihar';

-- 5. Chhattisgarh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Balod', 'Baloda Bazar', 'Balrampur', 'Bastar', 'Bemetara', 'Bijapur', 'Bilaspur',
'Dantewada', 'Dhamtari', 'Durg', 'Gariaband', 'Janjgir-Champa', 'Jashpur', 'Kabirdham',
'Kanker', 'Kondagaon', 'Korba', 'Koriya', 'Mahasamund', 'Mungeli', 'Narayanpur',
'Raigarh', 'Raipur', 'Rajnandgaon', 'Sukma', 'Surajpur', 'Surguja', 'Gaurela-Pendra-Marwahi',
'Mohla-Manpur-Ambagarh Chowki', 'Sarangarh-Bilaigarh', 'Khairagarh-Chhuikhadan-Gandai',
'Manendragarh-Chirmiri-Bharatpur'
]), master_state_id FROM public.master_state WHERE state_name = 'Chhattisgarh';

-- 6. Goa districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'North Goa', 'South Goa'
]), master_state_id FROM public.master_state WHERE state_name = 'Goa';

-- 7. Gujarat districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Ahmedabad', 'Amreli', 'Anand', 'Aravalli', 'Banaskantha', 'Bharuch', 'Bhavnagar',
'Botad', 'Chhota Udaipur', 'Dahod', 'Dang', 'Devbhoomi Dwarka', 'Gandhinagar',
'Gir Somnath', 'Jamnagar', 'Junagadh', 'Kheda', 'Kutch', 'Mahisagar', 'Mehsana',
'Morbi', 'Narmada', 'Navsari', 'Panchmahal', 'Patan', 'Porbandar', 'Rajkot',
'Sabarkantha', 'Surat', 'Surendranagar', 'Tapi', 'Vadodara', 'Valsad'
]), master_state_id FROM public.master_state WHERE state_name = 'Gujarat';

-- 8. Haryana districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Ambala', 'Bhiwani', 'Charkhi Dadri', 'Faridabad', 'Fatehabad', 'Gurugram', 'Hisar',
'Jhajjar', 'Jind', 'Kaithal', 'Karnal', 'Kurukshetra', 'Mahendragarh', 'Nuh',
'Palwal', 'Panchkula', 'Panipat', 'Rewari', 'Rohtak', 'Sirsa', 'Sonipat', 'Yamunanagar'
]), master_state_id FROM public.master_state WHERE state_name = 'Haryana';

-- 9. Himachal Pradesh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Bilaspur', 'Chamba', 'Hamirpur', 'Kangra', 'Kinnaur', 'Kullu', 'Lahaul and Spiti',
'Mandi', 'Shimla', 'Sirmaur', 'Solan', 'Una'
]), master_state_id FROM public.master_state WHERE state_name = 'Himachal Pradesh';

-- 10. Jharkhand districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Bokaro', 'Chatra', 'Deoghar', 'Dhanbad', 'Dumka', 'East Singhbhum', 'Garhwa',
'Giridih', 'Godda', 'Gumla', 'Hazaribagh', 'Jamtara', 'Khunti', 'Koderma', 'Latehar',
'Lohardaga', 'Pakur', 'Palamu', 'Ramgarh', 'Ranchi', 'Sahibganj', 'Seraikela Kharsawan',
'Simdega', 'West Singhbhum'
]), master_state_id FROM public.master_state WHERE state_name = 'Jharkhand';

-- 11. Karnataka districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Bagalkot', 'Bangalore Rural', 'Bangalore Urban', 'Belgaum', 'Bellary', 'Bidar',
'Bijapur', 'Chamarajanagar', 'Chikkaballapur', 'Chikkamagaluru', 'Chitradurga',
'Dakshina Kannada', 'Davanagere', 'Dharwad', 'Gadag', 'Gulbarga', 'Hassan', 'Haveri',
'Kodagu', 'Kolar', 'Koppal', 'Mandya', 'Mysore', 'Raichur', 'Ramanagara', 'Shimoga',
'Tumkur', 'Udupi', 'Uttara Kannada', 'Vijayapura', 'Yadgir', 'Vijayanagara'
]), master_state_id FROM public.master_state WHERE state_name = 'Karnataka';

-- 12. Kerala districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Alappuzha', 'Ernakulam', 'Idukki', 'Kannur', 'Kasaragod', 'Kollam', 'Kottayam',
'Kozhikode', 'Malappuram', 'Palakkad', 'Pathanamthitta', 'Thiruvananthapuram',
'Thrissur', 'Wayanad'
]), master_state_id FROM public.master_state WHERE state_name = 'Kerala';

-- 13. Madhya Pradesh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Agar Malwa', 'Alirajpur', 'Anuppur', 'Ashoknagar', 'Balaghat', 'Barwani', 'Betul',
'Bhind', 'Bhopal', 'Burhanpur', 'Chhatarpur', 'Chhindwara', 'Damoh', 'Datia', 'Dewas',
'Dhar', 'Dindori', 'Guna', 'Gwalior', 'Harda', 'Hoshangabad', 'Indore', 'Jabalpur',
'Jhabua', 'Katni', 'Khandwa', 'Khargone', 'Mandla', 'Mandsaur', 'Morena', 'Narsinghpur',
'Neemuch', 'Panna', 'Raisen', 'Rajgarh', 'Ratlam', 'Rewa', 'Sagar', 'Satna', 'Sehore',
'Seoni', 'Shahdol', 'Shajapur', 'Sheopur', 'Shivpuri', 'Sidhi', 'Singrauli', 'Tikamgarh',
'Ujjain', 'Umaria', 'Vidisha', 'Niwari'
]), master_state_id FROM public.master_state WHERE state_name = 'Madhya Pradesh';

-- 14. Maharashtra districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Ahmednagar', 'Akola', 'Amravati', 'Aurangabad', 'Beed', 'Bhandara', 'Buldhana',
'Chandrapur', 'Dhule', 'Gadchiroli', 'Gondia', 'Hingoli', 'Jalgaon', 'Jalna', 'Kolhapur',
'Latur', 'Mumbai City', 'Mumbai Suburban', 'Nagpur', 'Nanded', 'Nandurbar', 'Nashik',
'Osmanabad', 'Palghar', 'Parbhani', 'Pune', 'Raigad', 'Ratnagiri', 'Sangli', 'Satara',
'Sindhudurg', 'Solapur', 'Thane', 'Wardha', 'Washim', 'Yavatmal'
]), master_state_id FROM public.master_state WHERE state_name = 'Maharashtra';

-- 15. Manipur districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Bishnupur', 'Chandel', 'Churachandpur', 'Imphal East', 'Imphal West', 'Jiribam',
'Kakching', 'Kamjong', 'Kangpokpi', 'Noney', 'Pherzawl', 'Senapati', 'Tamenglong',
'Tengnoupal', 'Thoubal', 'Ukhrul'
]), master_state_id FROM public.master_state WHERE state_name = 'Manipur';

-- 16. Meghalaya districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'East Khasi Hills', 'East Jaintia Hills', 'West Jaintia Hills', 'West Khasi Hills',
'South West Khasi Hills', 'East Garo Hills', 'West Garo Hills', 'South West Garo Hills',
'South Garo Hills', 'North Garo Hills', 'Eastern West Khasi Hills'
]), master_state_id FROM public.master_state WHERE state_name = 'Meghalaya';

-- 17. Mizoram districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Aizawl', 'Champhai', 'Kolasib', 'Lawngtlai', 'Lunglei', 'Mamit', 'Saiha', 'Serchhip',
'Hnahthial', 'Khawzawl', 'Saitual'
]), master_state_id FROM public.master_state WHERE state_name = 'Mizoram';

-- 18. Nagaland districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Dimapur', 'Kiphire', 'Kohima', 'Longleng', 'Mokokchung', 'Mon', 'Peren', 'Phek',
'Tuensang', 'Wokha', 'Zunheboto', 'Noklak', 'Tseminyu', 'Niuland', 'Chumoukedima', 'Shamator'
]), master_state_id FROM public.master_state WHERE state_name = 'Nagaland';

-- 19. Odisha districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Angul', 'Balangir', 'Balasore', 'Bargarh', 'Bhadrak', 'Boudh', 'Cuttack', 'Deogarh',
'Dhenkanal', 'Gajapati', 'Ganjam', 'Jagatsinghpur', 'Jajpur', 'Jharsuguda', 'Kalahandi',
'Kandhamal', 'Kendrapara', 'Kendujhar', 'Khordha', 'Koraput', 'Malkangiri', 'Mayurbhanj',
'Nabarangpur', 'Nayagarh', 'Nuapada', 'Puri', 'Rayagada', 'Sambalpur', 'Subarnapur', 'Sundargarh'
]), master_state_id FROM public.master_state WHERE state_name = 'Odisha';

-- 20. Punjab districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Amritsar', 'Barnala', 'Bathinda', 'Faridkot', 'Fatehgarh Sahib', 'Fazilka',
'Ferozepur', 'Gurdaspur', 'Hoshiarpur', 'Jalandhar', 'Kapurthala', 'Ludhiana',
'Mansa', 'Moga', 'Muktsar', 'Pathankot', 'Patiala', 'Rupnagar', 'Sahibzada Ajit Singh Nagar',
'Sangrur', 'Shahid Bhagat Singh Nagar', 'Tarn Taran', 'Malerkotla'
]), master_state_id FROM public.master_state WHERE state_name = 'Punjab';

-- 21. Rajasthan districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Ajmer', 'Alwar', 'Banswara', 'Baran', 'Barmer', 'Bharatpur', 'Bhilwara', 'Bikaner',
'Bundi', 'Chittorgarh', 'Churu', 'Dausa', 'Dholpur', 'Dungarpur', 'Hanumangarh',
'Jaipur', 'Jaisalmer', 'Jalore', 'Jhalawar', 'Jhunjhunu', 'Jodhpur', 'Karauli',
'Kota', 'Nagaur', 'Pali', 'Pratapgarh', 'Rajsamand', 'Sawai Madhopur', 'Sikar',
'Sirohi', 'Sri Ganganagar', 'Tonk', 'Udaipur'
]), master_state_id FROM public.master_state WHERE state_name = 'Rajasthan';

-- 22. Sikkim districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'East Sikkim', 'North Sikkim', 'South Sikkim', 'West Sikkim', 'Pakyong', 'Soreng'
]), master_state_id FROM public.master_state WHERE state_name = 'Sikkim';

-- 23. Tamil Nadu districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Ariyalur', 'Chennai', 'Coimbatore', 'Cuddalore', 'Dharmapuri', 'Dindigul', 'Erode',
'Kanchipuram', 'Kanyakumari', 'Karur', 'Krishnagiri', 'Madurai', 'Nagapattinam',
'Namakkal', 'Nilgiris', 'Perambalur', 'Pudukkottai', 'Ramanathapuram', 'Salem',
'Sivaganga', 'Thanjavur', 'Theni', 'Thoothukudi', 'Tiruchirappalli', 'Tirunelveli',
'Tiruppur', 'Tiruvallur', 'Tiruvannamalai', 'Tiruvarur', 'Vellore', 'Viluppuram',
'Virudhunagar', 'Tenkasi', 'Chengalpattu', 'Ranipet', 'Kallakurichi', 'Tirupattur', 'Mayiladuthurai'
]), master_state_id FROM public.master_state WHERE state_name = 'Tamil Nadu';

-- 24. Telangana districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Adilabad', 'Bhadradri Kothagudem', 'Hyderabad', 'Jagtial', 'Jangaon', 'Jayashankar Bhupalpally',
'Jogulamba Gadwal', 'Kamareddy', 'Karimnagar', 'Khammam', 'Komaram Bheem Asifabad', 'Mahabubabad',
'Mahabubnagar', 'Mancherial', 'Medak', 'Medchal-Malkajgiri', 'Mulugu', 'Nagarkurnool',
'Nalgonda', 'Narayanpet', 'Nirmal', 'Nizamabad', 'Peddapalli', 'Rajanna Sircilla', 'Rangareddy',
'Sangareddy', 'Siddipet', 'Suryapet', 'Vikarabad', 'Wanaparthy', 'Warangal', 'Yadadri Bhuvanagiri',
'Hanumakonda', 'Hanamkonda'
]), master_state_id FROM public.master_state WHERE state_name = 'Telangana';

-- 25. Tripura districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Dhalai', 'Gomati', 'Khowai', 'North Tripura', 'Sepahijala', 'South Tripura',
'Unakoti', 'West Tripura'
]), master_state_id FROM public.master_state WHERE state_name = 'Tripura';

-- 26. Uttar Pradesh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Agra', 'Aligarh', 'Allahabad', 'Ambedkar Nagar', 'Amethi', 'Amroha', 'Auraiya',
'Azamgarh', 'Baghpat', 'Bahraich', 'Ballia', 'Balrampur', 'Banda', 'Barabanki',
'Bareilly', 'Basti', 'Bhadohi', 'Bijnor', 'Budaun', 'Bulandshahr', 'Chandauli',
'Chitrakoot', 'Deoria', 'Etah', 'Etawah', 'Faizabad', 'Farrukhabad', 'Fatehpur',
'Firozabad', 'Gautam Buddha Nagar', 'Ghaziabad', 'Ghazipur', 'Gonda', 'Gorakhpur',
'Hamirpur', 'Hapur', 'Hardoi', 'Hathras', 'Jalaun', 'Jaunpur', 'Jhansi', 'Kannauj',
'Kanpur Dehat', 'Kanpur Nagar', 'Kasganj', 'Kaushambi', 'Kheri', 'Kushinagar',
'Lalitpur', 'Lucknow', 'Maharajganj', 'Mahoba', 'Mainpuri', 'Mathura', 'Mau', 'Meerut',
'Mirzapur', 'Moradabad', 'Muzaffarnagar', 'Pilibhit', 'Pratapgarh', 'Raebareli',
'Rampur', 'Saharanpur', 'Sambhal', 'Sant Kabir Nagar', 'Shahjahanpur', 'Shamli',
'Shravasti', 'Siddharthnagar', 'Sitapur', 'Sonbhadra', 'Sultanpur', 'Unnao', 'Varanasi'
]), master_state_id FROM public.master_state WHERE state_name = 'Uttar Pradesh';

-- 27. Uttarakhand districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Almora', 'Bageshwar', 'Chamoli', 'Champawat', 'Dehradun', 'Haridwar', 'Nainital',
'Pauri Garhwal', 'Pithoragarh', 'Rudraprayag', 'Tehri Garhwal', 'Udham Singh Nagar', 'Uttarkashi'
]), master_state_id FROM public.master_state WHERE state_name = 'Uttarakhand';

-- 28. West Bengal districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Alipurduar', 'Bankura', 'Birbhum', 'Cooch Behar', 'Dakshin Dinajpur', 'Darjeeling',
'Hooghly', 'Howrah', 'Jalpaiguri', 'Jhargram', 'Kalimpong', 'Kolkata', 'Malda',
'Murshidabad', 'Nadia', 'North 24 Parganas', 'Paschim Bardhaman', 'Paschim Medinipur',
'Purba Bardhaman', 'Purba Medinipur', 'Purulia', 'South 24 Parganas', 'Uttar Dinajpur'
]), master_state_id FROM public.master_state WHERE state_name = 'West Bengal';

-- Union Territories

-- 29. Andaman and Nicobar Islands districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Nicobar', 'North and Middle Andaman', 'South Andaman'
]), master_state_id FROM public.master_state WHERE state_name = 'Andaman and Nicobar Islands';

-- 30. Chandigarh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Chandigarh'
]), master_state_id FROM public.master_state WHERE state_name = 'Chandigarh';

-- 31. Dadra and Nagar Haveli and Daman and Diu districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Dadra and Nagar Haveli', 'Daman', 'Diu'
]), master_state_id FROM public.master_state WHERE state_name = 'Dadra and Nagar Haveli and Daman and Diu';

-- 32. Delhi districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Central Delhi', 'East Delhi', 'New Delhi', 'North Delhi', 'North East Delhi',
'North West Delhi', 'Shahdara', 'South Delhi', 'South East Delhi', 'South West Delhi', 'West Delhi'
]), master_state_id FROM public.master_state WHERE state_name = 'Delhi';

-- 33. Jammu and Kashmir districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Anantnag', 'Bandipora', 'Baramulla', 'Budgam', 'Doda', 'Ganderbal', 'Jammu',
'Kathua', 'Kishtwar', 'Kulgam', 'Kupwara', 'Poonch', 'Pulwama', 'Rajouri',
'Ramban', 'Reasi', 'Samba', 'Shopian', 'Srinagar', 'Udhampur'
]), master_state_id FROM public.master_state WHERE state_name = 'Jammu and Kashmir';

-- 34. Ladakh districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Kargil', 'Leh'
]), master_state_id FROM public.master_state WHERE state_name = 'Ladakh';

-- 35. Lakshadweep district
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Lakshadweep'
]), master_state_id FROM public.master_state WHERE state_name = 'Lakshadweep';

-- 36. Puducherry districts
INSERT INTO public.master_district (district_name, master_state_id)
SELECT unnest(ARRAY[
'Karaikal', 'Mahe', 'Puducherry', 'Yanam'
]), master_state_id FROM public.master_state WHERE state_name = 'Puducherry';

--08 -05- 2025

CREATE TABLE public.entity_type_master (
    entity_name character varying PRIMARY KEY,
    display_name character varying NOT NULL,
    value_chain_type character varying,
    file_type public.file_type
);

ALTER TABLE public.entity_type_master OWNER TO postgres;



INSERT INTO public.entity_type_master (
    entity_name, display_name, value_chain_type, file_type
) VALUES
    ('Farmer', 'Farmer', NULL, 'MASTER'),
    ('Staff', 'Staff', NULL, 'MASTER'),
    ('Centre', 'Centre', NULL, 'MASTER');



ALTER TABLE public.excel_file_meta_data
ALTER COLUMN entity_type TYPE character varying,
ALTER COLUMN entity_type SET NOT NULL;

ALTER TABLE public.excel_file_meta_data_history
ALTER COLUMN entity_type TYPE character varying,
ALTER COLUMN entity_type SET NOT NULL;

ALTER TABLE public.column_mapping_template
ALTER COLUMN entity_type TYPE character varying,
ALTER COLUMN entity_type SET NOT NULL;



drop type entity_type;


-- Add MilkQuality entity type to entity_type_master table
INSERT INTO public.entity_type_master (
    entity_name, display_name, value_chain_type, file_type
) VALUES
    ('MilkQuality', 'Milk Quality', 'Dairy', 'TRANSACTIONAL');



CREATE TABLE public.milk_quality (
    milk_quality_id bigint NOT NULL,
    date date,
    program_id bigint,
    partner_id bigint,
    centre_id character varying(255),
    total_milk_volume numeric(38,2) DEFAULT 0.00,
    fat numeric(38,2) DEFAULT 0.00,
    clr numeric(38,2) DEFAULT 0.00,
    snf numeric(38,2) DEFAULT 0.00,
    segregated_milk_volume numeric(38,2) DEFAULT 0.00,
    ab_positive_milk_volume numeric(38,2) DEFAULT 0.00,
    afm1_positive_milk_volume numeric(38,2) DEFAULT 0.00,
    high_sodium_milk_volume numeric(38,2) DEFAULT 0.00,
    overall_positive_milk_volume numeric(38,2) DEFAULT 0.00,
    overall_negative_milk_volume numeric(38,2) DEFAULT 0.00,
    qbi_volume_milk_volume numeric(38,2) DEFAULT 0.00,
    beta_pos_volume numeric(38,2) DEFAULT 0.00,
    sulfa_pos_volume numeric(38,2) DEFAULT 0.00,
    tetra_pos_volume numeric(38,2) DEFAULT 0.00,
    cap_pos_volume numeric(38,2) DEFAULT 0.00,
    excel_file_meta_data_id bigint,
    CONSTRAINT milk_quality_pkey PRIMARY KEY (milk_quality_id),
    CONSTRAINT fk_milk_quality_program FOREIGN KEY (program_id) REFERENCES public.program(program_id),
    CONSTRAINT fk_milk_quality_partner FOREIGN KEY (partner_id) REFERENCES public.partner(partner_id),

    CONSTRAINT fk_excel_file_meta_data FOREIGN KEY (excel_file_meta_data_id)
        REFERENCES public.excel_file_meta_data (excel_file_meta_data_id)
);

ALTER TABLE public.milk_quality OWNER TO postgres;

ALTER TABLE public.milk_quality ALTER COLUMN milk_quality_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.milk_quality_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

-- Add DairyFieldData entity type to entity_type_master table
INSERT INTO public.entity_type_master (
    entity_name, display_name, value_chain_type, file_type
) VALUES
    ('DairyFieldData', 'Field Data', 'Dairy', 'TRANSACTIONAL');

CREATE TABLE public.dairy_field_data (
    dairy_field_data_id bigint NOT NULL,
    date date,
    program_id bigint,
    partner_id bigint,
    total_farmers integer,
    no_of_animal_welfare_farms integer,
    no_of_women_empowerment integer,
    excel_file_meta_data_id bigint,

    CONSTRAINT dairy_field_data_pkey PRIMARY KEY (dairy_field_data_id),
    CONSTRAINT fk_dairy_field_data_excel_file_meta_data FOREIGN KEY (excel_file_meta_data_id)
        REFERENCES public.excel_file_meta_data (excel_file_meta_data_id),
    CONSTRAINT fk_dairy_field_data_program FOREIGN KEY (program_id)
        REFERENCES public.program (program_id),
    CONSTRAINT fk_dairy_field_data_partner FOREIGN KEY (partner_id)
        REFERENCES public.partner (partner_id)
);

ALTER TABLE public.dairy_field_data OWNER TO postgres;

ALTER TABLE public.dairy_field_data ALTER COLUMN dairy_field_data_id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.dairy_field_data_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

INSERT INTO public.centre_type (type)
VALUES
  ('VLCC'),
  ('BMC'),
  ('MCC'),
  ('ICS'),
  ('FPO'),
  ('CPG');



CREATE TABLE IF NOT EXISTS public.currency (
    code character varying(3) NOT NULL,
    CONSTRAINT currency_pkey PRIMARY KEY (code)
);

INSERT INTO public.currency (code) VALUES
('AED'), ('AFN'), ('ALL'), ('AMD'), ('ANG'), ('AOA'), ('ARS'), ('AUD'), ('AWG'), ('AZN'),
('BAM'), ('BBD'), ('BDT'), ('BGN'), ('BHD'), ('BIF'), ('BMD'), ('BND'), ('BOB'), ('BRL'),
('BSD'), ('BTN'), ('BWP'), ('BYN'), ('BZD'), ('CAD'), ('CDF'), ('CHF'), ('CLP'), ('CNY'),
('COP'), ('CRC'), ('CUP'), ('CVE'), ('CZK'), ('DJF'), ('DKK'), ('DOP'), ('DZD'), ('EGP'),
('ERN'), ('ETB'), ('EUR'), ('FJD'), ('FKP'), ('FOK'), ('GBP'), ('GEL'), ('GGP'), ('GHS'),
('GIP'), ('GMD'), ('GNF'), ('GTQ'), ('GYD'), ('HKD'), ('HNL'), ('HRK'), ('HTG'), ('HUF'),
('IDR'), ('ILS'), ('IMP'), ('INR'), ('IQD'), ('IRR'), ('ISK'), ('JEP'), ('JMD'), ('JOD'),
('JPY'), ('KES'), ('KGS'), ('KHR'), ('KID'), ('KMF'), ('KRW'), ('KWD'), ('KYD'), ('KZT'),
('LAK'), ('LBP'), ('LKR'), ('LRD'), ('LSL'), ('LYD'), ('MAD'), ('MDL'), ('MGA'), ('MKD'),
('MMK'), ('MNT'), ('MOP'), ('MRU'), ('MUR'), ('MVR'), ('MWK'), ('MXN'), ('MYR'), ('MZN'),
('NAD'), ('NGN'), ('NIO'), ('NOK'), ('NPR'), ('NZD'), ('OMR'), ('PAB'), ('PEN'), ('PGK'),
('PHP'), ('PKR'), ('PLN'), ('PYG'), ('QAR'), ('RON'), ('RSD'), ('RUB'), ('RWF'), ('SAR'),
('SBD'), ('SCR'), ('SDG'), ('SEK'), ('SGD'), ('SHP'), ('SLE'), ('SOS'), ('SRD'), ('SSP'),
('STN'), ('SYP'), ('SZL'), ('THB'), ('TJS'), ('TMT'), ('TND'), ('TOP'), ('TRY'), ('TTD'),
('TVD'), ('TWD'), ('TZS'), ('UAH'), ('UGX'), ('USD'), ('UYU'), ('UZS'), ('VES'), ('VND'),
('VUV'), ('WST'), ('XAF'), ('XCD'), ('XOF'), ('XPF'), ('YER'), ('ZAR'), ('ZMW'), ('ZWL');


alter table public.fund_code add currency character varying(3) default 'USD';
alter table public.fund_code add constraint fk_fund_code_currency foreign key (currency) references public.currency(code);


INSERT INTO public.entity_type_master (
    entity_name, display_name, value_chain_type, file_type
) VALUES
    ('CottonFarmingData', 'Cotton Farming Data', 'Cotton', 'TRANSACTIONAL');

CREATE TABLE IF NOT EXISTS public.cotton_farming_data
(
    farmer_id character varying NOT NULL,
    program_id bigint NOT NULL,
    partner_id bigint NOT NULL,
    year integer NOT NULL,
    males_in_household integer,
    females_in_household integer,
    children_in_household integer,
    school_going_children integer,
    earning_members integer,
    total_landholding double precision,
    primary_crop character varying,
    secondary_crops character varying,
    non_organic_cotton_land double precision,
    organic_cotton_land double precision,
    years_organic_practice integer,
    certification_status character varying,
    irrigation_source character varying,
    cattle_count integer,
    drinking_water_source character varying,
    preferred_selling_point character varying,
    has_storage_space character varying,
    receives_agro_advisory character varying,
    received_training character varying,
    membership_in_org character varying,
    maintains_records character varying,
    annual_household_income numeric(38,2),
    primary_income_source character varying,
    primary_income_amount numeric(38,2),
    certification_cost_per_acre numeric(38,2),
    avg_production_per_acre double precision,
    cost_of_cultivation_per_acre numeric(38,2),
    organic_cotton_quantity_sold double precision,
    selling_price_per_kg numeric(38,2),
    bio_inputs_cost numeric(38,2),
    pest_management_bio_inputs character varying,
    bio_fertilizer_used character varying,
    pheromone_traps_per_acre integer,
    pheromone_traps_price numeric(38,2),
    yellow_sticky_traps_per_acre integer,
    yellow_sticky_traps_price numeric(38,2),
    blue_sticky_traps_per_acre integer,
    blue_sticky_traps_price numeric(38,2),
    bird_perches_per_acre integer,
    irrigation_cost_per_acre numeric(38,2),
    irrigation_count integer,
    irrigation_method character varying,
    farm_machinery_hired character varying,
    machinery_hiring_cost numeric(38,2),
    local_labour_cost_per_day numeric(38,2),
    migrant_labour_cost_per_day numeric(38,2),
    workers_for_sowing integer,
    workers_for_harvesting integer,
    harvesting_time character varying,
    weeding_method character varying,
    weeding_cost_per_acre numeric(38,2),
    mulching_cost_per_acre numeric(38,2),
    tillage_count integer,
    tillage_cost_per_acre numeric(38,2),
    land_preparation_cost numeric(38,2),
    organic_cotton_seed_rate double precision,
    organic_cotton_seed_variety character varying,
    border_crop character varying,
    inter_crop character varying,
    cover_crop character varying,
    trap_crop character varying,
    mulching_used character varying,
    mulching_type character varying,
    storage_precautions character varying,
    hired_vehicle_for_transport character varying,
    transportation_cost_per_kg numeric(38,2),
    rejected_quantity double precision,
    price_discovery_mechanism character varying,
    payment_transaction_type character varying,
    credit_days integer,
    govt_scheme_availed character varying,
    crop_insurance character varying,
    crop_insurance_cost_per_acre numeric(38,2),
    has_kcc character varying,
    has_active_bank_account character varying,
    crop_rotation_used character varying,
    rotation_crops character varying,
    water_tracking_devices character varying,
    pump_capacity integer,
    buffer_zone character varying,
    crop_residue_utilization character varying,
    worker_payment_mode character varying,
    wage_gender_difference character varying,
    labour_register character varying,
    safety_kit_for_workers character varying,
    shelter_and_water_for_workers character varying,
    lavatory_for_workers character varying,
    women_in_agri_operations character varying,
    community_water_harvesting character varying,
    soil_moisture_meter_used character varying,
    excel_file_meta_data_id bigint,
	total_hh_members integer,
    dependency_ratio double precision,
    gender_ratio double precision,
    school_attendance_rate double precision,
    total_cotton_land double precision,
    organic_percent double precision,
    land_used_for_cotton double precision,
    income_per_earner double precision,
    oc_income double precision,
    profit_per_acre double precision,
    total_certification_cost double precision,
    total_pt_cost double precision,
    total_yst_cost double precision,
    total_bst_cost double precision,
    total_pest_mgmt_cost double precision,
    total_labour_cost double precision,
    machinery_cost_total double precision,
    total_irrigation_cost double precision,
    irrigation_frequency double precision,

    CONSTRAINT cotton_farming_data_pkey PRIMARY KEY (farmer_id, program_id, partner_id, year),
    CONSTRAINT fk_cotton_farming_data_farmer FOREIGN KEY (farmer_id, program_id, partner_id)
        REFERENCES public.farmer (farmer_id, program_id, partner_id),
    CONSTRAINT fk_cotton_farming_data_program FOREIGN KEY (program_id)
        REFERENCES public.program (program_id),
    CONSTRAINT fk_cotton_farming_data_partner FOREIGN KEY (partner_id)
        REFERENCES public.partner (partner_id),
    CONSTRAINT fk_cotton_farming_data_excel_file FOREIGN KEY (excel_file_meta_data_id)
        REFERENCES public.excel_file_meta_data (excel_file_meta_data_id)
);

ALTER TABLE public.cotton_farming_data
    OWNER to postgres;

alter table farmer add farmer_tracenet_code character varying;

alter table farmer add highest_education character varying;


alter table  excel_file_meta_data add archived_on TIMESTAMP;

alter table milk_quality add  CONSTRAINT fk_milk_quality_centre FOREIGN KEY (centre_id, program_id, partner_id)
        REFERENCES public.centre (centre_id, program_id, partner_id);

alter table milk_quality add  CONSTRAINT fk_milk_quality_data_excel_file
FOREIGN KEY (excel_file_meta_data_id) REFERENCES public.excel_file_meta_data (excel_file_meta_data_id);